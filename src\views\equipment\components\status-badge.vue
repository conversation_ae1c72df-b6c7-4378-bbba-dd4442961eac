<script setup lang="ts">
/**
 * 设备状态徽章组件
 * 用于显示设备相关实体的启用/禁用状态
 * 与原React组件功能完全对等，支持自定义样式
 */

defineOptions({
  name: "EquipmentStatusBadge"
});

interface StatusBadgeProps {
  /** 是否启用状态 */
  enabled: boolean;
  /** 自定义CSS类名 */
  className?: string;
}

withDefaults(defineProps<StatusBadgeProps>(), {
  enabled: true,
  className: ""
});
</script>

<template>
  <el-tag
    :type="enabled ? 'success' : 'info'"
    :class="className"
    effect="light"
    size="default"
  >
    {{ enabled ? "启用" : "禁用" }}
  </el-tag>
</template>

<style lang="scss" scoped>
// 自定义状态徽章样式，保持与原React组件一致的视觉效果
:deep(.el-tag) {
  // 启用状态 - 绿色主题 (对应原组件的 bg-green-100 text-green-800)
  &.el-tag--success.el-tag--light {
    background-color: #dcfce7; // 对应 bg-green-100
    border-color: #bbf7d0;
    color: #166534; // 对应 text-green-800
  }
  
  // 禁用状态 - 灰色主题 (对应原组件的 bg-gray-100 text-gray-800)
  &.el-tag--info.el-tag--light {
    background-color: #f3f4f6; // 对应 bg-gray-100
    border-color: #d1d5db;
    color: #374151; // 对应 text-gray-800
  }
  
  // 禁用hover效果变化，保持一致的颜色 (对应原组件的 hover:bg-green-100)
  &:hover {
    background-color: var(--el-tag-bg-color);
    border-color: var(--el-tag-border-color);
    color: var(--el-tag-text-color);
  }
}
</style>