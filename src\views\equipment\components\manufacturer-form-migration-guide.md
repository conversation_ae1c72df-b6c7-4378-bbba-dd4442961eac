# ManufacturerForm 组件迁移指南

## 📋 组件概述

`manufacturer-form.vue` 是从React设备管理系统迁移而来的制造商表单组件，用于制造商信息的录入和编辑功能。

## 🎯 功能特性对比

### 原React组件特性
- ✅ 制造商名称输入（必填，1-100字符验证）
- ✅ 备注信息输入（可选，最多500字符）
- ✅ 启用状态切换（默认启用）
- ✅ 表单验证（Zod schema）
- ✅ 加载状态支持
- ✅ 新增/编辑模式支持
- ✅ 基于React Hook Form + shadcn/ui

### Vue3迁移后特性
- ✅ 完全对等的表单字段和验证
- ✅ Element Plus表单组件
- ✅ Vue3 reactive响应式数据管理
- ✅ Element Plus验证规则
- ✅ 保持一致的用户交互
- ✅ 基于Vue3 Composition API + Element Plus
- 🎉 **增强功能**：响应式布局适配
- 🎉 **增强功能**：明暗主题支持
- 🎉 **增强功能**：表单方法暴露
- 🎉 **增强功能**：禁用状态支持

## 📦 API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| initialData | `Manufacturer` | `undefined` | 初始数据（编辑模式时传入） |
| onSubmit | `(data: ManufacturerFormData) => void` | - | 表单提交回调（必填） |
| onCancel | `() => void` | - | 取消操作回调（必填） |
| isLoading | `boolean` | `false` | 是否处于加载状态 |

### Manufacturer 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| equipmentManufacturerId | `number?` | 制造商ID |
| manufacturerName | `string?` | 制造商名称 |
| remarks | `string?` | 备注信息 |
| enabled | `boolean?` | 启用状态 |
| createTime | `string?` | 创建时间 |
| creator | `string?` | 创建者 |
| editTime | `string?` | 编辑时间 |
| editor | `string?` | 编辑者 |

### ManufacturerFormData 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| manufacturerName | `string` | 制造商名称（必填） |
| remarks | `string?` | 备注信息（可选） |
| enabled | `boolean?` | 启用状态（可选） |

### 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getRef | - | `FormInstance` | 获取表单实例引用 |
| resetForm | - | `void` | 重置表单到初始状态 |
| handleSubmit | - | `void` | 手动触发表单提交 |

## 📦 使用方法

### 基本使用 - 新增模式

```vue
<script setup lang="ts">
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

const handleSubmit = (data: ManufacturerFormData) => {
  console.log("提交数据:", data);
  // 调用API创建制造商
  createManufacturer(data);
};

const handleCancel = () => {
  console.log("取消操作");
  // 关闭对话框或返回列表页
};
</script>

<template>
  <ManufacturerForm
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>
```

### 编辑模式使用

```vue
<script setup lang="ts">
import { ref } from "vue";
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

// 编辑数据
const manufacturerData = ref({
  equipmentManufacturerId: 1,
  manufacturerName: "华为技术有限公司",
  remarks: "全球领先的ICT基础设施和智能终端提供商",
  enabled: true,
  createTime: "2023-01-15 10:30:00"
});

const isLoading = ref(false);

const handleSubmit = async (data: ManufacturerFormData) => {
  try {
    isLoading.value = true;
    await updateManufacturer(manufacturerData.value.equipmentManufacturerId, data);
    ElMessage.success("更新成功");
  } catch (error) {
    ElMessage.error("更新失败");
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <ManufacturerForm
    :initial-data="manufacturerData"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

### 在对话框中使用

```vue
<script setup lang="ts">
import { h } from "vue";
import { addDialog } from "@/components/ReDialog";
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

function openManufacturerDialog(action: "新增" | "编辑", data?: Manufacturer) {
  addDialog({
    title: `${action}制造商`,
    width: "600px",
    draggable: true,
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: ({ options }) => h(ManufacturerForm, {
      initialData: data,
      onSubmit: async (formData) => {
        try {
          if (action === "新增") {
            await createManufacturer(formData);
          } else {
            await updateManufacturer(data?.equipmentManufacturerId, formData);
          }
          ElMessage.success(`${action}成功`);
          done(); // 关闭对话框
          onSearch(); // 刷新列表
        } catch (error) {
          ElMessage.error(`${action}失败`);
        }
      },
      onCancel: () => done()
    })
  });
}

// 使用示例
const handleAdd = () => openManufacturerDialog("新增");
const handleEdit = (row: Manufacturer) => openManufacturerDialog("编辑", row);
</script>
```

### 表单验证和重置

```vue
<script setup lang="ts">
import { ref } from "vue";

const formRef = ref();

// 获取表单引用并进行自定义验证
const validateForm = () => {
  const form = formRef.value?.getRef();
  form?.validate((valid: boolean) => {
    if (valid) {
      console.log("验证通过");
    } else {
      console.log("验证失败");
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetForm();
};

// 手动提交
const submitForm = () => {
  formRef.value?.handleSubmit();
};
</script>

<template>
  <div>
    <ManufacturerForm
      ref="formRef"
      :on-submit="handleSubmit"
      :on-cancel="handleCancel"
    />
    
    <div class="custom-actions">
      <el-button @click="validateForm">验证表单</el-button>
      <el-button @click="resetForm">重置表单</el-button>
      <el-button @click="submitForm">手动提交</el-button>
    </div>
  </div>
</template>
```

## 🔄 迁移现有代码

### 1. 替换React表单组件

**原React代码:**
```tsx
import { ManufacturerForm } from "@/components/manufacturer-form";

const formSchema = z.object({
  manufacturer_name: z.string().min(1).max(100),
  remarks: z.string().max(500).optional(),
  enabled: z.boolean().default(true),
});

<ManufacturerForm 
  initialData={manufacturerData}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  isLoading={isLoading}
/>
```

**Vue3代码:**
```vue
<script setup lang="ts">
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

// 验证规则已内置在组件中，无需外部定义
const handleSubmit = (data: ManufacturerFormData) => {
  // 处理提交逻辑
};
</script>

<template>
  <ManufacturerForm 
    :initial-data="manufacturerData"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

### 2. 验证规则迁移

**React (Zod schema):**
```typescript
const formSchema = z.object({
  manufacturer_name: z.string().min(1, "制造商名称不能为空").max(100, "制造商名称不能超过100个字符"),
  remarks: z.string().max(500, "备注不能超过500个字符").optional(),
  enabled: z.boolean().default(true),
});
```

**Vue3 (Element Plus rules):**
```typescript
// 已内置在组件中
const formRules = {
  manufacturerName: [
    { required: true, message: "制造商名称不能为空", trigger: "blur" },
    { max: 100, message: "制造商名称不能超过100个字符", trigger: "blur" }
  ],
  remarks: [
    { max: 500, message: "备注不能超过500个字符", trigger: "blur" }
  ]
};
```

### 3. 表单状态管理迁移

**React (useForm):**
```typescript
const form = useForm<ManufacturerFormData>({
  resolver: zodResolver(formSchema),
  defaultValues: {
    manufacturer_name: initialData?.manufacturer_name || "",
    remarks: initialData?.remarks || "",
    enabled: initialData?.enabled ?? true,
  },
});
```

**Vue3 (reactive):**
```typescript
// 组件内部使用reactive管理状态
const formData = reactive<ManufacturerFormData>({
  manufacturerName: props.initialData?.manufacturerName || "",
  remarks: props.initialData?.remarks || "",
  enabled: props.initialData?.enabled ?? true
});
```

## 🎨 样式定制

### 自定义表单样式

```vue
<template>
  <ManufacturerForm 
    class="custom-manufacturer-form"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>

<style scoped>
.custom-manufacturer-form {
  :deep(.el-form-item__label) {
    color: var(--el-color-primary);
    font-weight: 600;
  }
  
  :deep(.status-switch-container) {
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary-light-7);
  }
}
</style>
```

### 自定义按钮样式

```vue
<style scoped>
.custom-manufacturer-form {
  :deep(.form-actions) {
    justify-content: center;
    gap: 24px;
    
    .el-button {
      min-width: 120px;
    }
  }
}
</style>
```

## 🔧 技术实现细节

### 表单数据管理

Vue3使用reactive进行响应式数据管理：

```typescript
// 与props.initialData同步
const formData = reactive<ManufacturerFormData>({
  manufacturerName: props.initialData?.manufacturerName || "",
  remarks: props.initialData?.remarks || "",
  enabled: props.initialData?.enabled ?? true
});
```

### 验证规则转换

从Zod schema转换为Element Plus规则：

```typescript
// React Zod
z.string().min(1, "不能为空").max(100, "不能超过100个字符")

// Vue3 Element Plus
[
  { required: true, message: "不能为空", trigger: "blur" },
  { max: 100, message: "不能超过100个字符", trigger: "blur" }
]
```

### 布局系统

使用ReCol组件实现响应式布局：

```vue
<el-row :gutter="20">
  <re-col :value="24">
    <el-form-item>
      <!-- 表单项内容 -->
    </el-form-item>
  </re-col>
</el-row>
```

## 🧪 测试用例

```vue
<script setup lang="ts">
// 测试数据
const testCases = [
  {
    name: "新增模式",
    data: undefined,
    expected: "空表单，enabled默认为true"
  },
  {
    name: "编辑模式 - 标准数据",
    data: {
      manufacturerName: "华为技术",
      remarks: "科技公司",
      enabled: true
    },
    expected: "表单填充完整数据"
  },
  {
    name: "编辑模式 - 最小数据",
    data: {
      manufacturerName: "小米",
      enabled: false
    },
    expected: "仅名称和状态，备注为空"
  },
  {
    name: "长文本测试",
    data: {
      manufacturerName: "比亚迪股份有限公司",
      remarks: "很长的备注信息...".repeat(20),
      enabled: true
    },
    expected: "测试长文本显示和验证"
  }
];

// 验证测试
const runValidationTests = () => {
  console.log("运行验证测试...");
  // 测试必填验证
  // 测试长度限制验证
  // 测试格式验证
};
</script>

<template>
  <div v-for="test in testCases" :key="test.name">
    <h4>{{ test.name }}</h4>
    <ManufacturerForm 
      :initial-data="test.data"
      :on-submit="handleSubmit"
      :on-cancel="handleCancel"
    />
    <p>期望结果：{{ test.expected }}</p>
  </div>
</template>
```

## 🚀 最佳实践

1. **数据初始化**: 编辑模式时确保传入完整且正确的initialData
2. **错误处理**: 在onSubmit回调中添加完善的错误处理逻辑
3. **加载状态**: 在异步操作期间正确设置isLoading状态
4. **表单验证**: 利用组件暴露的方法进行自定义验证控制
5. **用户体验**: 适当使用ElMessage提供操作反馈
6. **响应式**: 组件已内置响应式支持，无需额外处理

## 📋 迁移检查清单

- [ ] 导入ManufacturerForm组件
- [ ] 定义ManufacturerFormData接口
- [ ] 实现onSubmit回调函数
- [ ] 实现onCancel回调函数
- [ ] 测试新增模式功能
- [ ] 测试编辑模式功能
- [ ] 验证表单验证规则
- [ ] 测试加载状态
- [ ] 检查响应式布局
- [ ] 验证明暗主题切换
- [ ] 更新相关文档

## 🔗 相关文件

- `manufacturer-form.vue` - 主组件文件
- `manufacturer-form-example.vue` - 使用示例
- `manufacturer-form-migration-guide.md` - 本迁移指南

## ❓ 常见问题

**Q: 如何自定义验证规则？**
A: 可以修改组件内的formRules对象，或通过ref获取表单实例进行自定义验证。

**Q: 如何处理表单提交失败？**
A: 在onSubmit回调中使用try-catch处理错误，并设置相应的isLoading状态。

**Q: 能否添加更多表单字段？**
A: 可以扩展组件，添加新的字段和相应的验证规则。

**Q: 如何自定义布局？**
A: 可以通过SCSS覆盖组件样式，或修改ReCol的配置。

**Q: 支持表单联动吗？**
A: 当前版本字段相对独立，如需联动可以通过watch监听formData变化实现。

**Q: 如何集成到现有的表格管理页面？**
A: 参考示例中的对话框集成方式，配合addDialog使用。