# PageHeader 组件迁移指南

## 📋 组件概述

`page-header.vue` 是从React设备管理系统迁移而来的页面头部组件，用于显示页面标题、描述信息，并提供返回和新增等操作功能。

## 🎯 功能特性对比

### 原React组件特性
- ✅ 显示页面标题和描述
- ✅ 可选的返回按钮
- ✅ 可选的新增按钮，支持自定义文字
- ✅ Flex布局，左右两端对齐
- ✅ 使用Lucide图标库
- ✅ 基于shadcn/ui Button组件

### Vue3迁移后特性
- ✅ 完全对等的功能实现
- ✅ 使用Element Plus Button组件
- ✅ 使用项目统一的图标系统
- ✅ 保持一致的视觉效果和布局
- ✅ 完整的TypeScript类型支持
- ✅ 响应式布局适配
- ✅ 明暗主题支持

## 📦 API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | `string` | - | 页面标题（必填） |
| description | `string` | `""` | 页面描述信息 |
| showBack | `boolean` | `false` | 是否显示返回按钮 |
| onBack | `() => void` | `() => {}` | 返回按钮点击回调 |
| showAdd | `boolean` | `false` | 是否显示新增按钮 |
| onAdd | `() => void` | `() => {}` | 新增按钮点击回调 |
| addLabel | `string` | `"新增"` | 新增按钮文字 |

### 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onBack | 返回按钮点击时触发 | - |
| onAdd | 新增按钮点击时触发 | - |

## 📦 使用方法

### 基本使用

```vue
<script setup lang="ts">
import PageHeader from "@/views/equipment/components/page-header.vue";

// 事件处理函数
const handleBack = () => {
  // 返回逻辑，如：router.go(-1)
};

const handleAdd = () => {
  // 新增逻辑，如：openDialog('新增')
};
</script>

<template>
  <!-- 最简单的使用 -->
  <PageHeader title="设备管理" />
  
  <!-- 带描述信息 -->
  <PageHeader 
    title="制造商管理" 
    description="管理设备制造商信息，包括新增、编辑、删除等操作"
  />
  
  <!-- 带返回按钮 -->
  <PageHeader 
    title="制造商详情" 
    :show-back="true"
    :on-back="handleBack"
  />
  
  <!-- 带新增按钮 -->
  <PageHeader 
    title="制造商管理" 
    :show-add="true"
    :on-add="handleAdd"
    add-label="新增制造商"
  />
  
  <!-- 完整功能 -->
  <PageHeader 
    title="联系人管理" 
    description="管理制造商和供应商的联系人信息"
    :show-back="true"
    :on-back="handleBack"
    :show-add="true"
    :on-add="handleAdd"
    add-label="新增联系人"
  />
</template>
```

### 在页面中的典型使用

```vue
<template>
  <div class="equipment-page">
    <!-- 页面头部 -->
    <PageHeader 
      title="设备类型管理" 
      description="管理设备分类信息，支持树形结构"
      :show-add="true"
      :on-add="openDialog"
      add-label="新增设备类型"
    />
    
    <!-- 页面主要内容 -->
    <div class="page-content">
      <!-- 搜索表单、表格等内容 -->
    </div>
  </div>
</template>

<style scoped>
.equipment-page {
  padding: 20px;
}

.page-content {
  margin-top: 20px;
}
</style>
```

## 🔄 迁移现有代码

### 1. 替换页面标题区域

**原有代码 (需要替换):**
```vue
<template>
  <div class="page-title-area">
    <div class="title-section">
      <h1>设备管理</h1>
      <p>管理设备信息</p>
    </div>
    <div class="action-section">
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>
  </div>
</template>
```

**新代码 (推荐):**
```vue
<script setup lang="ts">
import PageHeader from "@/views/equipment/components/page-header.vue";
</script>

<template>
  <PageHeader 
    title="设备管理"
    description="管理设备信息"
    :show-add="true"
    :on-add="handleAdd"
    add-label="新增"
  />
</template>
```

### 2. 添加返回功能

```vue
<script setup lang="ts">
import { useRouter } from "vue-router";

const router = useRouter();

const handleBack = () => {
  // 返回上一页
  router.go(-1);
  
  // 或者返回指定页面
  // router.push('/equipment');
};
</script>

<template>
  <PageHeader 
    title="设备详情"
    :show-back="true"
    :on-back="handleBack"
  />
</template>
```

## 🎨 样式定制

### 自定义样式

```vue
<template>
  <PageHeader 
    title="自定义样式示例"
    class="custom-page-header"
  />
</template>

<style scoped>
.custom-page-header {
  padding: 24px 0;
  border-bottom: 1px solid var(--el-border-color);
}

// 深度选择器修改内部样式
:deep(.page-header__title) {
  color: var(--el-color-primary);
}
</style>
```

### 响应式布局

组件内置了响应式支持，在移动端会自动调整为垂直布局：

```scss
// 自动适配，无需额外代码
@media (max-width: 768px) {
  // 组件会自动变为垂直布局
  // 标题和按钮会分行显示
}
```

## 🔧 技术实现细节

### 图标系统对比

| React原始 | Vue3实现 | 说明 |
|-----------|----------|------|
| `<ArrowLeft className="h-4 w-4" />` | `:icon="useRenderIcon(ArrowLeft)"` | 16px大小的返回图标 |
| `<Plus className="h-4 w-4 mr-2" />` | `:icon="useRenderIcon(Plus)"` | 16px大小的新增图标 |

### 按钮样式对比

| React原始 | Vue3实现 | 说明 |
|-----------|----------|------|
| `variant="ghost" size="sm"` | `circle text size="default"` | 返回按钮样式 |
| `默认按钮样式` | `type="primary"` | 新增按钮样式 |

### 布局实现

```scss
// React: className="flex items-center justify-between"
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// React: className="flex items-center gap-4"
.page-header__left {
  display: flex;
  align-items: center;
  gap: 16px; // 对应gap-4
}
```

## 🧪 测试用例

```vue
<script setup lang="ts">
import { ref } from "vue";
import PageHeader from "./page-header.vue";

const testCases = [
  {
    name: "仅标题",
    props: { title: "测试标题" }
  },
  {
    name: "标题+描述",
    props: { title: "测试标题", description: "测试描述" }
  },
  {
    name: "带返回按钮",
    props: { title: "测试标题", showBack: true }
  },
  {
    name: "带新增按钮",
    props: { title: "测试标题", showAdd: true }
  },
  {
    name: "完整功能",
    props: { 
      title: "测试标题", 
      description: "测试描述",
      showBack: true,
      showAdd: true,
      addLabel: "自定义新增"
    }
  }
];
</script>

<template>
  <div v-for="test in testCases" :key="test.name">
    <h3>{{ test.name }}</h3>
    <PageHeader v-bind="test.props" />
  </div>
</template>
```

## 🚀 最佳实践

1. **标题规范**: 使用简洁明了的标题，避免过长
2. **描述信息**: 可选使用，提供页面功能的简要说明
3. **按钮使用**: 
   - 返回按钮：用于详情页、编辑页等子页面
   - 新增按钮：用于列表页面，文字可以具体化
4. **响应式**: 组件已内置响应式支持，无需额外处理
5. **一致性**: 在同一模块中保持交互和文案的一致性

## 📋 迁移检查清单

- [ ] 导入PageHeader组件
- [ ] 替换现有的页面标题区域
- [ ] 配置正确的props
- [ ] 实现事件回调函数
- [ ] 验证功能对等性
- [ ] 检查响应式布局
- [ ] 测试明暗主题切换
- [ ] 更新相关文档

## 🔗 相关文件

- `page-header.vue` - 主组件文件
- `page-header-example.vue` - 使用示例
- `page-header-migration-guide.md` - 本迁移指南

## ❓ 常见问题

**Q: 如何自定义返回按钮的行为？**
A: 通过onBack回调函数实现，可以使用router.go(-1)或跳转到指定页面。

**Q: 新增按钮可以自定义样式吗？**
A: 可以通过CSS深度选择器修改，或者在父组件中重写样式。

**Q: 组件是否支持插槽？**
A: 当前版本不支持插槽，如需扩展可以在组件中添加slot支持。

**Q: 如何在移动端优化？**
A: 组件已内置响应式支持，会自动在移动端调整为垂直布局。