<script setup lang="ts" generic="T extends Record<string, any>">
/**
 * 设备数据表格组件
 * 从React设备管理系统迁移而来的通用表格组件
 * 与原React组件功能完全对等，支持泛型、自定义渲染、操作菜单等
 */
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import MoreFilled from "~icons/ep/more-filled";
import View from "~icons/ep/view";
import Edit from "~icons/ep/edit";
import Delete from "~icons/ep/delete";
import User from "~icons/ep/user";

defineOptions({
  name: "EquipmentDataTable"
});

// 列配置接口
interface Column<T> {
  key: keyof T | string;
  title: string;
  width?: number | string;
  minWidth?: number | string;
  align?: "left" | "center" | "right";
  fixed?: "left" | "right";
  showOverflowTooltip?: boolean;
  render?: (value: any, record: T, index: number) => any;
}

// 组件Props接口
interface DataTableProps<T> {
  /** 表格数据 */
  data: T[];
  /** 列配置 */
  columns: Column<T>[];
  /** 是否显示边框 */
  border?: boolean;
  /** 是否显示斑马纹 */
  stripe?: boolean;
  /** 表格大小 */
  size?: "large" | "default" | "small";
  /** 加载状态 */
  loading?: boolean;
  /** 行唯一标识字段名 */
  rowKey?: string;
  /** 查看操作回调 */
  onView?: (record: T) => void;
  /** 编辑操作回调 */
  onEdit?: (record: T) => void;
  /** 删除操作回调 */
  onDelete?: (record: T) => void;
  /** 管理联系人操作回调 */
  onManageContacts?: (record: T) => void;
}

const props = withDefaults(defineProps<DataTableProps<T>>(), {
  border: true,
  stripe: true,
  size: "default",
  loading: false,
  rowKey: "id"
});

// 获取行数据的值
const getCellValue = (record: T, key: keyof T | string): any => {
  return record[key as keyof T];
};

// 渲染单元格内容
const renderCell = (column: Column<T>, record: T, index: number) => {
  const value = getCellValue(record, column.key);
  
  if (column.render) {
    return column.render(value, record, index);
  }
  
  return value || "-";
};

// 检查是否有任何操作按钮
const hasAnyAction = () => {
  return !!(props.onView || props.onEdit || props.onDelete || props.onManageContacts);
};

// 处理操作命令
const handleCommand = (command: string, record: T) => {
  switch (command) {
    case "view":
      props.onView?.(record);
      break;
    case "edit":
      props.onEdit?.(record);
      break;
    case "contacts":
      props.onManageContacts?.(record);
      break;
    case "delete":
      props.onDelete?.(record);
      break;
  }
};
</script>

<template>
  <div class="equipment-data-table">
    <el-table
      :data="data"
      :border="border"
      :stripe="stripe"
      :size="size"
      :loading="loading"
      :row-key="rowKey"
      empty-text="暂无数据"
      style="width: 100%"
      class="equipment-table"
    >
      <!-- 动态列渲染 -->
      <el-table-column
        v-for="column in columns"
        :key="String(column.key)"
        :prop="String(column.key)"
        :label="column.title"
        :width="column.width"
        :min-width="column.minWidth"
        :align="column.align || 'left'"
        :fixed="column.fixed"
        :show-overflow-tooltip="column.showOverflowTooltip"
      >
        <template #default="{ row, $index }">
          <!-- 如果有自定义渲染函数，使用渲染函数 -->
          <component
            v-if="column.render"
            :is="() => renderCell(column, row, $index)"
          />
          <!-- 否则显示普通文本 -->
          <span v-else>
            {{ getCellValue(row, column.key) || "-" }}
          </span>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        v-if="hasAnyAction()"
        label="操作"
        width="100"
        fixed="right"
        align="center"
      >
        <template #default="{ row }">
          <el-dropdown
            trigger="click"
            placement="bottom-end"
            @command="(command: string) => handleCommand(command, row)"
          >
            <el-button
              :icon="useRenderIcon(MoreFilled)"
              circle
              text
              size="small"
              class="operation-trigger"
            />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="onView"
                  command="view"
                  :icon="useRenderIcon(View)"
                >
                  查看
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="onEdit"
                  command="edit"
                  :icon="useRenderIcon(Edit)"
                >
                  编辑
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="onManageContacts"
                  command="contacts"
                  :icon="useRenderIcon(User)"
                >
                  管理联系人
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="onDelete"
                  command="delete"
                  :icon="useRenderIcon(Delete)"
                  class="danger-item"
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style lang="scss" scoped>
.equipment-data-table {
  .equipment-table {
    // 对应原React组件的 rounded-md border 样式
    border-radius: 6px;
    
    // 自定义表头样式
    :deep(.el-table__header) {
      .el-table__cell {
        background-color: var(--el-fill-color-light);
        color: var(--el-text-color-primary);
        font-weight: 500;
      }
    }
    
    // 操作列按钮样式
    :deep(.operation-trigger) {
      // 对应原组件 h-8 w-8 p-0 的样式
      width: 32px;
      height: 32px;
      padding: 0;
      
      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
    
    // 空数据状态样式
    :deep(.el-table__empty-block) {
      // 对应原组件 h-24 text-center 的样式
      min-height: 96px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    // 行悬停效果
    :deep(.el-table__row) {
      &:hover {
        background-color: var(--el-table-row-hover-bg-color);
      }
    }
  }
}

// 危险操作样式
:deep(.danger-item) {
  .el-dropdown-menu__item {
    color: var(--el-color-danger);
    
    &:hover {
      background-color: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }
  }
}

// 暗色主题适配
.dark {
  .equipment-data-table {
    .equipment-table {
      :deep(.el-table__header) {
        .el-table__cell {
          background-color: var(--el-fill-color-dark);
          color: var(--el-text-color-primary);
        }
      }
    }
  }
}
</style>