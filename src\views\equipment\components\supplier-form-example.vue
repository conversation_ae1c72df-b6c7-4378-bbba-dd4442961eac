<script setup lang="ts">
/**
 * SupplierForm组件使用示例
 * 展示如何在项目中使用迁移后的供应商表单组件
 */
import { ref, reactive } from "vue";
import SupplierForm from "./supplier-form.vue";

defineOptions({
  name: "SupplierFormUsageExample"
});

// 模拟制造商数据类型
interface Manufacturer {
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  remarks?: string;
  enabled?: boolean;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 模拟供应商数据类型
interface Supplier {
  equipmentSupplierId?: number;
  supplierName?: string;
  supplierAddress?: string;
  remarks?: string;
  enabled?: boolean;
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 模拟表单数据类型
interface SupplierFormData {
  supplierName: string;
  supplierAddress?: string;
  remarks?: string;
  enabled?: boolean;
  equipmentManufacturerId: number;
}

// 状态管理
const currentMode = ref<"create" | "edit">("create");
const isLoading = ref(false);
const showForm = ref(true);

// 模拟制造商数据
const manufacturerList = reactive<Manufacturer[]>([
  {
    equipmentManufacturerId: 1,
    manufacturerName: "华为技术有限公司",
    remarks: "全球领先的ICT基础设施和智能终端提供商",
    enabled: true,
    createTime: "2023-01-15 10:30:00",
    creator: "系统管理员"
  },
  {
    equipmentManufacturerId: 2,
    manufacturerName: "小米科技有限公司",
    remarks: "专注于智能硬件和电子产品研发",
    enabled: true,
    createTime: "2023-02-20 14:20:00",
    creator: "业务管理员"
  },
  {
    equipmentManufacturerId: 3,
    manufacturerName: "比亚迪股份有限公司",
    remarks: "新能源汽车及动力电池制造商",
    enabled: false, // 禁用状态，不会在下拉中显示
    createTime: "2023-03-10 09:15:00",
    creator: "业务管理员"
  },
  {
    equipmentManufacturerId: 4,
    manufacturerName: "腾讯科技有限公司",
    remarks: "互联网综合服务提供商",
    enabled: true,
    createTime: "2023-04-05 16:45:00",
    creator: "系统管理员"
  }
]);

// 模拟编辑数据
const editData = reactive<Supplier>({
  equipmentSupplierId: 1,
  supplierName: "深圳市创新供应链有限公司",
  supplierAddress: "深圳市南山区科技园南区深南大道10000号",
  remarks: "专业的电子产品供应链服务商，与华为技术有限公司建立长期合作关系。主要提供高品质的电子元器件和相关技术支持服务。",
  enabled: true,
  equipmentManufacturerId: 1,
  manufacturerName: "华为技术有限公司",
  createTime: "2023-01-20 11:00:00",
  creator: "采购管理员"
});

// 模拟表单提交
const handleSubmit = (data: SupplierFormData) => {
  console.log("表单提交数据:", data);
  
  isLoading.value = true;
  
  // 模拟API调用
  setTimeout(() => {
    isLoading.value = false;
    
    if (currentMode.value === "create") {
      ElMessage.success(`成功创建供应商「${data.supplierName}」`);
    } else {
      ElMessage.success(`成功更新供应商「${data.supplierName}」`);
    }
    
    // 模拟表单重置或关闭
    // showForm.value = false;
  }, 2000);
};

// 取消操作
const handleCancel = () => {
  console.log("取消操作");
  ElMessage.info("已取消操作");
  showForm.value = false;
};

// 切换模式
const switchMode = (mode: "create" | "edit") => {
  currentMode.value = mode;
  showForm.value = true;
  isLoading.value = false;
};

// 重新显示表单
const showFormAgain = () => {
  showForm.value = true;
};

// 切换制造商状态
const toggleManufacturerStatus = (id: number) => {
  const manufacturer = manufacturerList.find(m => m.equipmentManufacturerId === id);
  if (manufacturer) {
    manufacturer.enabled = !manufacturer.enabled;
    ElMessage.info(`${manufacturer.manufacturerName} 状态已${manufacturer.enabled ? '启用' : '禁用'}`);
  }
};

// 添加制造商
const addManufacturer = () => {
  const newId = Math.max(...manufacturerList.map(m => m.equipmentManufacturerId!)) + 1;
  manufacturerList.push({
    equipmentManufacturerId: newId,
    manufacturerName: `新制造商${newId}`,
    remarks: "新添加的制造商",
    enabled: true,
    createTime: new Date().toLocaleString(),
    creator: "示例用户"
  });
  ElMessage.success("已添加新制造商");
};

// 长地址示例数据
const longAddressData = reactive<Supplier>({
  equipmentSupplierId: 2,
  supplierName: "上海智能物流有限公司",
  supplierAddress: "上海市浦东新区张江高科技园区科学城路100号张江药谷公共服务平台1号楼5层501-508室",
  remarks: "专业从事智能物流解决方案的高新技术企业，业务覆盖仓储管理、运输配送、供应链金融等多个领域。",
  enabled: false,
  equipmentManufacturerId: 2,
  manufacturerName: "小米科技有限公司",
  createTime: "2023-02-25 16:30:00",
  creator: "物流管理员"
});

// 最小数据示例
const minimalData = reactive<Supplier>({
  supplierName: "简单供应商",
  enabled: true,
  equipmentManufacturerId: 1
});
</script>

<template>
  <div class="supplier-form-usage-example">
    <el-card header="SupplierForm组件使用示例">
      
      <!-- 制造商管理控制 -->
      <el-divider>制造商数据管理</el-divider>
      <div class="manufacturer-controls">
        <div class="manufacturer-list">
          <el-tag
            v-for="manufacturer in manufacturerList"
            :key="manufacturer.equipmentManufacturerId"
            :type="manufacturer.enabled ? 'success' : 'danger'"
            closable
            @close="toggleManufacturerStatus(manufacturer.equipmentManufacturerId!)"
            style="margin: 4px;"
          >
            {{ manufacturer.manufacturerName }}
            {{ manufacturer.enabled ? '(启用)' : '(禁用)' }}
          </el-tag>
        </div>
        <el-button size="small" @click="addManufacturer">添加制造商</el-button>
      </div>

      <!-- 模式切换控制 -->
      <el-divider>模式切换</el-divider>
      <div class="mode-controls">
        <el-radio-group v-model="currentMode" @change="switchMode">
          <el-radio-button value="create">新增模式</el-radio-button>
          <el-radio-button value="edit">编辑模式</el-radio-button>
        </el-radio-group>
        
        <el-button 
          v-if="!showForm"
          type="primary"
          @click="showFormAgain"
        >
          重新显示表单
        </el-button>
      </div>

      <!-- 当前模式信息 -->
      <el-alert
        :title="`当前模式: ${currentMode === 'create' ? '新增供应商' : '编辑供应商'}`"
        :type="currentMode === 'create' ? 'success' : 'warning'"
        show-icon
        :closable="false"
        style="margin: 16px 0;"
      />

      <!-- 表单示例 -->
      <el-divider>表单示例</el-divider>
      <div v-if="showForm" class="form-container">
        <SupplierForm
          :initial-data="currentMode === 'edit' ? editData : undefined"
          :manufacturers="manufacturerList"
          :on-submit="handleSubmit"
          :on-cancel="handleCancel"
          :is-loading="isLoading"
        />
      </div>
      
      <div v-else class="form-hidden">
        <el-result
          icon="success"
          title="表单已隐藏"
          sub-title="点击上方按钮重新显示表单"
        />
      </div>

      <!-- 不同数据状态示例 -->
      <el-divider>不同数据状态示例</el-divider>
      <div class="data-examples">
        <el-tabs>
          <el-tab-pane label="标准数据" name="standard">
            <div class="example-data">
              <h4>标准供应商数据：</h4>
              <pre><code>{{ JSON.stringify(editData, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="长地址数据" name="long">
            <div class="example-data">
              <h4>包含长地址的供应商数据：</h4>
              <pre><code>{{ JSON.stringify(longAddressData, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="最小数据" name="minimal">
            <div class="example-data">
              <h4>最小供应商数据：</h4>
              <pre><code>{{ JSON.stringify(minimalData, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="制造商列表" name="manufacturers">
            <div class="example-data">
              <h4>可用制造商列表：</h4>
              <pre><code>{{ JSON.stringify(manufacturerList, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 功能对比说明 -->
      <el-divider>功能对比</el-divider>
      <div class="comparison-section">
        <el-descriptions title="React vs Vue3 功能对比" :column="1" border>
          <el-descriptions-item label="组件名称">
            <el-tag type="info">React: SupplierForm</el-tag>
            <el-tag type="success">Vue3: EquipmentSupplierForm</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="表单字段">
            <div>
              <div>• supplierName: 供应商名称（必填，1-100字符）</div>
              <div>• supplierAddress: 供应商地址（可选，最多200字符）</div>
              <div>• equipmentManufacturerId: 关联制造商（必填）</div>
              <div>• remarks: 备注信息（可选，最多500字符）</div>
              <div>• enabled: 启用状态（布尔值，默认true）</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="关联功能">
            <div>✅ 制造商下拉选择（只显示已启用制造商）</div>
          </el-descriptions-item>
          <el-descriptions-item label="验证规则">
            <div>
              <el-tag type="info">React: Zod Schema</el-tag>
              <el-tag type="success">Vue3: Element Plus Rules</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="表单管理">
            <div>
              <el-tag type="info">React: useForm + zodResolver</el-tag>
              <el-tag type="success">Vue3: reactive + el-form</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="UI组件">
            <div>
              <el-tag type="info">React: shadcn/ui</el-tag>
              <el-tag type="success">Vue3: Element Plus</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="增强功能">
            <div>
              <div>✅ 制造商空状态提示</div>
              <div>✅ 响应式布局适配</div>
              <div>✅ 明暗主题支持</div>
              <div>✅ 表单方法暴露</div>
              <div>✅ 过滤和搜索功能</div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 代码示例 -->
      <el-divider>代码示例</el-divider>
      <div class="code-examples">
        <el-collapse>
          <el-collapse-item title="基本使用 - 新增模式" name="create">
            <pre><code>{{ createModeCode }}</code></pre>
          </el-collapse-item>
          <el-collapse-item title="编辑模式使用" name="edit">
            <pre><code>{{ editModeCode }}</code></pre>
          </el-collapse-item>
          <el-collapse-item title="制造商数据处理" name="manufacturers">
            <pre><code>{{ manufacturersCode }}</code></pre>
          </el-collapse-item>
          <el-collapse-item title="完整集成示例" name="integration">
            <pre><code>{{ integrationCode }}</code></pre>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 最佳实践建议 -->
      <el-divider>最佳实践建议</el-divider>
      <div class="best-practices">
        <el-alert
          title="使用建议"
          type="info"
          :closable="false"
        >
          <template #default>
            <div class="practices-list">
              <p><strong>1. 制造商数据：</strong>确保传入有效的制造商列表，至少包含一个启用的制造商</p>
              <p><strong>2. 数据初始化：</strong>编辑模式时确保传入完整的initialData，包括正确的制造商关联</p>
              <p><strong>3. 加载状态：</strong>在异步操作期间设置isLoading为true，禁用表单控件</p>
              <p><strong>4. 错误处理：</strong>在onSubmit中添加适当的错误处理和用户反馈</p>
              <p><strong>5. 数据验证：</strong>利用内置验证规则，也可以通过ref进行自定义验证</p>
              <p><strong>6. 制造商过滤：</strong>组件会自动过滤只显示启用的制造商，无需外部处理</p>
              <p><strong>7. 响应式：</strong>组件已内置响应式支持，在移动端自动调整布局</p>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
// 代码示例
const createModeCode = `<script setup lang="ts">
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

// 制造商列表（通常从API获取）
const manufacturerList = ref<Manufacturer[]>([
  { equipmentManufacturerId: 1, manufacturerName: "华为技术", enabled: true },
  { equipmentManufacturerId: 2, manufacturerName: "小米科技", enabled: true }
]);

const handleSubmit = (data: SupplierFormData) => {
  // 调用API创建供应商
  createSupplier(data);
};

const handleCancel = () => {
  // 取消操作，如关闭对话框
  closeDialog();
};
</script>

<template>
  <SupplierForm
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>`;

const editModeCode = `<script setup lang="ts">
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

const supplierData = ref({
  equipmentSupplierId: 1,
  supplierName: "深圳创新供应链",
  supplierAddress: "深圳市南山区科技园",
  equipmentManufacturerId: 1,
  remarks: "专业供应链服务商",
  enabled: true
});

const manufacturerList = ref<Manufacturer[]>([...]);
const isLoading = ref(false);

const handleSubmit = async (data: SupplierFormData) => {
  try {
    isLoading.value = true;
    await updateSupplier(supplierData.value.equipmentSupplierId, data);
    ElMessage.success("更新成功");
  } catch (error) {
    ElMessage.error("更新失败");
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <SupplierForm
    :initial-data="supplierData"
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>`;

const manufacturersCode = `<script setup lang="ts">
// 获取制造商列表
const manufacturerList = ref<Manufacturer[]>([]);

const loadManufacturers = async () => {
  try {
    const data = await equipmentManufacturerQueryController.getManufacturers();
    // 组件会自动过滤启用状态，这里无需过滤
    manufacturerList.value = data;
  } catch (error) {
    ElMessage.error("加载制造商列表失败");
    manufacturerList.value = [];
  }
};

// 监听制造商变化
watch(manufacturerList, (newList) => {
  const enabledCount = newList.filter(m => m.enabled).length;
  if (enabledCount === 0) {
    ElMessage.warning("暂无可用制造商，请先添加制造商");
  }
});

onMounted(() => {
  loadManufacturers();
});
</script>`;

const integrationCode = `<script setup lang="ts">
import { h } from "vue";
import { addDialog } from "@/components/ReDialog";
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

function openSupplierDialog(action: "新增" | "编辑", data?: Supplier) {
  addDialog({
    title: \`\${action}供应商\`,
    width: "700px",
    draggable: true,
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: ({ options }) => h(SupplierForm, {
      initialData: data,
      manufacturers: manufacturerList.value,
      onSubmit: async (formData) => {
        try {
          if (action === "新增") {
            await createSupplier(formData);
          } else {
            await updateSupplier(data?.equipmentSupplierId, formData);
          }
          ElMessage.success(\`\${action}成功\`);
          done(); // 关闭对话框
          onSearch(); // 刷新列表
        } catch (error) {
          ElMessage.error(\`\${action}失败\`);
        }
      },
      onCancel: () => done()
    })
  });
}

// 使用示例
const handleAdd = () => openSupplierDialog("新增");
const handleEdit = (row: Supplier) => openSupplierDialog("编辑", row);
</script>`;
</script>

<style lang="scss" scoped>
.supplier-form-usage-example {
  padding: 20px;
}

.manufacturer-controls {
  margin: 16px 0;
  
  .manufacturer-list {
    margin-bottom: 12px;
    min-height: 40px;
    padding: 8px;
    border: 1px dashed var(--el-border-color);
    border-radius: 4px;
    background-color: var(--el-fill-color-lighter);
  }
}

.mode-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}

.form-container {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  background-color: var(--el-bg-color-page);
}

.form-hidden {
  text-align: center;
  padding: 40px 20px;
}

.data-examples {
  margin: 16px 0;
  
  .example-data {
    h4 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
    }
    
    pre {
      background-color: var(--el-fill-color-lighter);
      padding: 16px;
      border-radius: 4px;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

.comparison-section {
  margin-top: 16px;
}

.code-examples {
  margin-top: 16px;
  
  pre {
    background-color: var(--el-fill-color-lighter);
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    
    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.best-practices {
  margin-top: 16px;
  
  .practices-list {
    p {
      margin: 8px 0;
      line-height: 1.6;
    }
  }
}

// 暗色主题适配
.dark {
  .form-container {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color);
  }
  
  .manufacturer-controls .manufacturer-list {
    background-color: var(--el-fill-color-darker);
    border-color: var(--el-border-color);
  }
  
  .data-examples,
  .code-examples {
    pre {
      background-color: var(--el-fill-color-darker);
    }
  }
}
</style>