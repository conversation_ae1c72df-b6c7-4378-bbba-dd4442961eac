// 制造商数据模型
export interface Manufacturer {
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  remarks?: string;
  enabled?: boolean;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
  contacts?: ManufacturerContact[];
}

// 供应商数据模型
export interface Supplier {
  equipmentSupplierId?: number;
  supplierName?: string;
  supplierAddress?: string;
  remarks?: string;
  equipmentManufacturerId?: number;
  equipmentManufacturerName?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
  contacts?: SupplierContact[];
}

// 制造商联系人数据模型
export interface ManufacturerContact {
  equipmentManufacturerContactId?: number;
  equipmentManufacturerId?: number;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 供应商联系人数据模型
export interface SupplierContact {
  equipmentSupplierContactId?: number;
  equipmentSupplierId?: number;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 搜索表单接口
interface SearchItemProps {
  manufacturerName: string;
}

// 制造商表单项接口
interface FormItemProps {
  equipmentManufacturerId: number | null;
  manufacturerName: string;
  remarks: string;
  enabled: boolean;
}

// 制造商联系人表单项接口
interface ContactFormItemProps {
  equipmentManufacturerContactId: number | null;
  equipmentManufacturerId: number;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  contactRole: string;
}

// 制造商表单属性接口
interface FormProps {
  formInline: FormItemProps;
}

// 制造商联系人表单属性接口
interface ContactFormProps {
  formInline: ContactFormItemProps;
}

// 搜索表单属性接口
interface SearchProps {
  formInline: SearchItemProps;
}

// 表单数据类型
export interface ManufacturerFormData {
  manufacturerName: string;
  remarks?: string;
}

export interface SupplierFormData {
  supplierName: string;
  supplierAddress?: string;
  remarks?: string;
  equipmentManufacturerId: number;
  sortOrder: number;
}

export interface ManufacturerContactFormData {
  contactName: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  equipmentManufacturerId: number;
}

export interface SupplierContactFormData {
  contactName: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  equipmentSupplierId: number;
}

export type { FormProps, ContactFormProps, SearchProps };
