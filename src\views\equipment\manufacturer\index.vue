<script setup lang="ts">
import { ref } from "vue";
import { useManufacturer } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import EnhancedTable from "@/components/EnhancedTable/index.vue";
import Delete from "~icons/ep/delete";
import EditPen from "~icons/ep/edit-pen";
import Refresh from "~icons/ep/refresh";
import AddFill from "~icons/ri/add-circle-line";
import User from "~icons/ep/user";

defineOptions({
  name: "Manufacturer"
});

const formRef = ref();
const tableRef = ref();
const {
  form,
  loading,
  columns,
  dataList,
  onSearch,
  resetForm,
  openDialog,
  handleDelete,
  onbatchDel,
  openContactDialog
} = useManufacturer();

function onFullscreen() {
  // 重置表格高度
  tableRef.value?.getTableRef()?.setAdaptive();
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  console.log("制造商选中项变化:", selection);
};

// 处理清除选择
const handleClearSelection = () => {
  console.log("清除制造商选择");
};
</script>

<template>
  <div class="main">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto rounded-lg"
    >
      <el-form-item label="制造商名称：" prop="manufacturerName">
        <el-input
          v-model="form.manufacturerName"
          placeholder="请输入制造商名称"
          clearable
          class="w-[180px]!"
          @keyup.enter="onSearch"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri/search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon(Refresh)"
          @click="resetForm(formRef, true)"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar
      title="制造商管理"
      :columns="columns"
      :tableRef="tableRef?.getTableRef()"
      @refresh="onSearch"
      @fullscreen="onFullscreen"
    >
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog('新增')"
        >
          新增制造商
        </el-button>
      </template>

      <template v-slot="{ size, dynamicColumns }">
        <EnhancedTable
          ref="tableRef"
          adaptive
          stripe
          :adaptiveConfig="{ offsetBottom: 45 }"
          align-whole="center"
          row-key="equipmentManufacturerId"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @clear-selection="handleClearSelection"
        >
          <!-- 自定义操作列 -->
          <template #operation="{ row, isHovered, hasSelection }">
            <div class="operation-cell">
              <!-- 多选状态下显示提示 -->
              <div v-if="hasSelection" class="text-gray-400 text-sm">
                点击行选择
              </div>

              <!-- 非多选状态下显示操作按钮 -->
              <template v-else>
                <!-- 悬停时显示浮动操作 -->
                <Transition
                  enter-active-class="transition-all duration-200"
                  enter-from-class="opacity-0 scale-95"
                  enter-to-class="opacity-100 scale-100"
                  leave-active-class="transition-all duration-150"
                  leave-from-class="opacity-100 scale-100"
                  leave-to-class="opacity-0 scale-95"
                >
                  <div v-if="isHovered" class="floating-actions" @click.stop>
                    <el-button
                      size="small"
                      type="primary"
                      :icon="useRenderIcon(EditPen)"
                      @click="openDialog('修改', row)"
                    >
                      修改
                    </el-button>
                    <el-button
                      size="small"
                      type="success"
                      :icon="useRenderIcon(User)"
                      @click="openContactDialog(row)"
                    >
                      管理联系人
                    </el-button>
                    <el-popconfirm
                      :title="`确认删除制造商「${row.manufacturerName}」？`"
                      @confirm="handleDelete(row)"
                    >
                      <template #reference>
                        <el-button
                          size="small"
                          type="danger"
                          :icon="useRenderIcon(Delete)"
                        >
                          删除
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </div>
                </Transition>

                <!-- 默认显示的简单操作 -->
                <div v-if="!isHovered" class="simple-actions">
                  <el-button
                    link
                    type="primary"
                    size="small"
                    :icon="useRenderIcon(EditPen)"
                    @click.stop="openDialog('修改', row)"
                  >
                    修改
                  </el-button>
                  <el-button
                    link
                    type="success"
                    size="small"
                    :icon="useRenderIcon(User)"
                    @click.stop="openContactDialog(row)"
                  >
                    管理联系人
                  </el-button>
                  <el-button
                    link
                    type="danger"
                    size="small"
                    :icon="useRenderIcon(Delete)"
                    @click.stop="handleDelete(row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </div>
          </template>

          <!-- 批量操作插槽 -->
          <template #batch-actions="{ selection, clearSelection }">
            <el-popconfirm
              :title="`确认批量删除选中的 ${selection.length} 个制造商吗？`"
              @confirm="
                () => {
                  onbatchDel(selection);
                  clearSelection();
                }
              "
            >
              <template #reference>
                <el-button
                  size="small"
                  type="danger"
                  :icon="useRenderIcon(Delete)"
                >
                  批量删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </EnhancedTable>
      </template>
    </PureTableBar>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-table__inner-wrapper::before) {
  height: 0;
}

.main-content {
  margin: 24px 24px 0 !important;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

// 操作列样式
.operation-cell {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 32px;
}

.floating-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 6px;
  z-index: 10;
  background: white;
  padding: 4px 6px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--el-border-color-light);

  &:deep(.dark) {
    background: rgb(31, 41, 55);
    border-color: rgb(75, 85, 99);
  }
}

.simple-actions {
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: wrap;
}
</style>