<script setup lang="ts">
/**
 * ManufacturerForm组件使用示例
 * 展示如何在项目中使用迁移后的制造商表单组件
 */
import { ref, reactive } from "vue";
import ManufacturerForm from "./manufacturer-form.vue";

defineOptions({
  name: "ManufacturerFormUsageExample"
});

// 模拟制造商数据类型
interface Manufacturer {
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  remarks?: string;
  enabled?: boolean;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 模拟表单数据类型
interface ManufacturerFormData {
  manufacturerName: string;
  remarks?: string;
  enabled?: boolean;
}

// 状态管理
const currentMode = ref<"create" | "edit">("create");
const isLoading = ref(false);
const showForm = ref(true);

// 模拟编辑数据
const editData = reactive<Manufacturer>({
  equipmentManufacturerId: 1,
  manufacturerName: "华为技术有限公司",
  remarks: "全球领先的ICT基础设施和智能终端提供商，致力于构建万物互联的智能世界。",
  enabled: true,
  createTime: "2023-01-15 10:30:00",
  creator: "系统管理员"
});

// 模拟表单提交
const handleSubmit = (data: ManufacturerFormData) => {
  console.log("表单提交数据:", data);
  
  isLoading.value = true;
  
  // 模拟API调用
  setTimeout(() => {
    isLoading.value = false;
    
    if (currentMode.value === "create") {
      ElMessage.success(`成功创建制造商「${data.manufacturerName}」`);
    } else {
      ElMessage.success(`成功更新制造商「${data.manufacturerName}」`);
    }
    
    // 模拟表单重置或关闭
    // showForm.value = false;
  }, 2000);
};

// 取消操作
const handleCancel = () => {
  console.log("取消操作");
  ElMessage.info("已取消操作");
  showForm.value = false;
};

// 切换模式
const switchMode = (mode: "create" | "edit") => {
  currentMode.value = mode;
  showForm.value = true;
  isLoading.value = false;
};

// 重新显示表单
const showFormAgain = () => {
  showForm.value = true;
};

// 长文本示例数据
const longRemarksData = reactive<Manufacturer>({
  equipmentManufacturerId: 2,
  manufacturerName: "比亚迪股份有限公司",
  remarks: "比亚迪股份有限公司创立于1995年，是一家致力于"用技术创新，满足人们对美好生活的向往"的高新技术企业。比亚迪成立之初专注于充电电池业务，凭借20多年的不断创新，现已成长为全球新能源整体解决方案开拓者。比亚迪业务布局涵盖电子、汽车、新能源和轨道交通等领域，并在全球设立30多个工业园，实现全球六大洲的战略布局。",
  enabled: false,
  createTime: "2023-03-10 09:15:00",
  creator: "业务管理员"
});

// 最小数据示例
const minimalData = reactive<Manufacturer>({
  manufacturerName: "小米科技",
  enabled: true
});
</script>

<template>
  <div class="manufacturer-form-usage-example">
    <el-card header="ManufacturerForm组件使用示例">
      
      <!-- 模式切换控制 -->
      <el-divider>模式切换</el-divider>
      <div class="mode-controls">
        <el-radio-group v-model="currentMode" @change="switchMode">
          <el-radio-button value="create">新增模式</el-radio-button>
          <el-radio-button value="edit">编辑模式</el-radio-button>
        </el-radio-group>
        
        <el-button 
          v-if="!showForm"
          type="primary"
          @click="showFormAgain"
        >
          重新显示表单
        </el-button>
      </div>

      <!-- 当前模式信息 -->
      <el-alert
        :title="`当前模式: ${currentMode === 'create' ? '新增制造商' : '编辑制造商'}`"
        :type="currentMode === 'create' ? 'success' : 'warning'"
        show-icon
        :closable="false"
        style="margin: 16px 0;"
      />

      <!-- 表单示例 -->
      <el-divider>表单示例</el-divider>
      <div v-if="showForm" class="form-container">
        <ManufacturerForm
          :initial-data="currentMode === 'edit' ? editData : undefined"
          :on-submit="handleSubmit"
          :on-cancel="handleCancel"
          :is-loading="isLoading"
        />
      </div>
      
      <div v-else class="form-hidden">
        <el-result
          icon="success"
          title="表单已隐藏"
          sub-title="点击上方按钮重新显示表单"
        />
      </div>

      <!-- 不同数据状态示例 -->
      <el-divider>不同数据状态示例</el-divider>
      <div class="data-examples">
        <el-tabs>
          <el-tab-pane label="标准数据" name="standard">
            <div class="example-data">
              <h4>标准制造商数据：</h4>
              <pre><code>{{ JSON.stringify(editData, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="长文本数据" name="long">
            <div class="example-data">
              <h4>包含长备注的制造商数据：</h4>
              <pre><code>{{ JSON.stringify(longRemarksData, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="最小数据" name="minimal">
            <div class="example-data">
              <h4>最小制造商数据：</h4>
              <pre><code>{{ JSON.stringify(minimalData, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 功能对比说明 -->
      <el-divider>功能对比</el-divider>
      <div class="comparison-section">
        <el-descriptions title="React vs Vue3 功能对比" :column="1" border>
          <el-descriptions-item label="组件名称">
            <el-tag type="info">React: ManufacturerForm</el-tag>
            <el-tag type="success">Vue3: EquipmentManufacturerForm</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="表单字段">
            <div>
              <div>• manufacturerName: 制造商名称（必填，1-100字符）</div>
              <div>• remarks: 备注信息（可选，最多500字符）</div>
              <div>• enabled: 启用状态（布尔值，默认true）</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="验证规则">
            <div>
              <el-tag type="info">React: Zod Schema</el-tag>
              <el-tag type="success">Vue3: Element Plus Rules</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="表单管理">
            <div>
              <el-tag type="info">React: useForm + zodResolver</el-tag>
              <el-tag type="success">Vue3: reactive + el-form</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="UI组件">
            <div>
              <el-tag type="info">React: shadcn/ui</el-tag>
              <el-tag type="success">Vue3: Element Plus</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="布局系统">
            <div>
              <el-tag type="info">React: Tailwind CSS</el-tag>
              <el-tag type="success">Vue3: ReCol + SCSS</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="增强功能">
            <div>
              <div>✅ 响应式布局适配</div>
              <div>✅ 明暗主题支持</div>
              <div>✅ 表单方法暴露</div>
              <div>✅ 禁用状态支持</div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 代码示例 -->
      <el-divider>代码示例</el-divider>
      <div class="code-examples">
        <el-collapse>
          <el-collapse-item title="基本使用 - 新增模式" name="create">
            <pre><code>{{ createModeCode }}</code></pre>
          </el-collapse-item>
          <el-collapse-item title="编辑模式使用" name="edit">
            <pre><code>{{ editModeCode }}</code></pre>
          </el-collapse-item>
          <el-collapse-item title="完整集成示例" name="integration">
            <pre><code>{{ integrationCode }}</code></pre>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 最佳实践建议 -->
      <el-divider>最佳实践建议</el-divider>
      <div class="best-practices">
        <el-alert
          title="使用建议"
          type="info"
          :closable="false"
        >
          <template #default>
            <div class="practices-list">
              <p><strong>1. 数据初始化：</strong>编辑模式时确保传入完整的initialData</p>
              <p><strong>2. 加载状态：</strong>在异步操作期间设置isLoading为true</p>
              <p><strong>3. 错误处理：</strong>在onSubmit中添加适当的错误处理逻辑</p>
              <p><strong>4. 表单重置：</strong>可以通过ref调用resetForm方法重置表单</p>
              <p><strong>5. 验证规则：</strong>可以根据业务需求调整验证规则</p>
              <p><strong>6. 响应式：</strong>组件已内置响应式支持，在移动端自动调整布局</p>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
// 代码示例
const createModeCode = `<script setup lang="ts">
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

const handleSubmit = (data: ManufacturerFormData) => {
  // 调用API创建制造商
  createManufacturer(data);
};

const handleCancel = () => {
  // 取消操作，如关闭对话框
  closeDialog();
};
</script>

<template>
  <ManufacturerForm
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>`;

const editModeCode = `<script setup lang="ts">
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

const manufacturerData = ref({
  equipmentManufacturerId: 1,
  manufacturerName: "华为技术有限公司",
  remarks: "全球领先的ICT基础设施和智能终端提供商",
  enabled: true
});

const isLoading = ref(false);

const handleSubmit = async (data: ManufacturerFormData) => {
  try {
    isLoading.value = true;
    await updateManufacturer(manufacturerData.value.equipmentManufacturerId, data);
    ElMessage.success("更新成功");
  } catch (error) {
    ElMessage.error("更新失败");
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <ManufacturerForm
    :initial-data="manufacturerData"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>`;

const integrationCode = `<script setup lang="ts">
import { addDialog } from "@/components/ReDialog";
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

// 打开制造商表单对话框
function openManufacturerDialog(action: "新增" | "编辑", data?: Manufacturer) {
  addDialog({
    title: \`\${action}制造商\`,
    width: "600px",
    draggable: true,
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: ({ options }) => h(ManufacturerForm, {
      initialData: data,
      onSubmit: async (formData) => {
        try {
          if (action === "新增") {
            await createManufacturer(formData);
          } else {
            await updateManufacturer(data?.equipmentManufacturerId, formData);
          }
          ElMessage.success(\`\${action}成功\`);
          done(); // 关闭对话框
          onSearch(); // 刷新列表
        } catch (error) {
          ElMessage.error(\`\${action}失败\`);
        }
      },
      onCancel: () => done()
    }),
    beforeSure: (done, { options }) => {
      // 表单验证和提交逻辑已在组件内处理
      options.contentRenderer.onSubmit?.();
    }
  });
}
</script>`;
</script>

<style lang="scss" scoped>
.manufacturer-form-usage-example {
  padding: 20px;
}

.mode-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}

.form-container {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  background-color: var(--el-bg-color-page);
}

.form-hidden {
  text-align: center;
  padding: 40px 20px;
}

.data-examples {
  margin: 16px 0;
  
  .example-data {
    h4 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
    }
    
    pre {
      background-color: var(--el-fill-color-lighter);
      padding: 16px;
      border-radius: 4px;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

.comparison-section {
  margin-top: 16px;
}

.code-examples {
  margin-top: 16px;
  
  pre {
    background-color: var(--el-fill-color-lighter);
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    
    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.best-practices {
  margin-top: 16px;
  
  .practices-list {
    p {
      margin: 8px 0;
      line-height: 1.6;
    }
  }
}

// 暗色主题适配
.dark {
  .form-container {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color);
  }
  
  .data-examples,
  .code-examples {
    pre {
      background-color: var(--el-fill-color-darker);
    }
  }
}
</style>