<script setup lang="ts">
/**
 * 设备页面头部组件
 * 用于显示页面标题、描述信息，提供返回和新增等操作功能
 * 与原React组件功能完全对等
 */
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import ArrowLeft from "~icons/ep/arrow-left";
import Plus from "~icons/ep/plus";

defineOptions({
  name: "EquipmentPageHeader"
});

interface PageHeaderProps {
  /** 页面标题 */
  title: string;
  /** 页面描述 */
  description?: string;
  /** 是否显示返回按钮 */
  showBack?: boolean;
  /** 返回按钮点击回调 */
  onBack?: () => void;
  /** 是否显示新增按钮 */
  showAdd?: boolean;
  /** 新增按钮点击回调 */
  onAdd?: () => void;
  /** 新增按钮文字 */
  addLabel?: string;
}

withDefaults(defineProps<PageHeaderProps>(), {
  description: "",
  showBack: false,
  onBack: () => {},
  showAdd: false,
  onAdd: () => {},
  addLabel: "新增"
});
</script>

<template>
  <div class="page-header">
    <!-- 左侧区域：返回按钮 + 标题信息 -->
    <div class="page-header__left">
      <!-- 返回按钮 -->
      <el-button
        v-if="showBack"
        :icon="useRenderIcon(ArrowLeft)"
        circle
        text
        size="default"
        class="page-header__back-btn"
        @click="onBack"
      />
      
      <!-- 标题区域 -->
      <div class="page-header__title-section">
        <h1 class="page-header__title">{{ title }}</h1>
        <p v-if="description" class="page-header__description">
          {{ description }}
        </p>
      </div>
    </div>

    <!-- 右侧区域：新增按钮 -->
    <div v-if="showAdd" class="page-header__right">
      <el-button
        type="primary"
        :icon="useRenderIcon(Plus)"
        @click="onAdd"
      >
        {{ addLabel }}
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  
  &__left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  &__back-btn {
    // 对应原组件 variant="ghost" size="sm" 的样式
    --el-button-text-color: var(--el-text-color-regular);
    --el-button-hover-text-color: var(--el-color-primary);
    --el-button-hover-bg-color: var(--el-fill-color-light);
    
    width: 32px;
    height: 32px;
    
    // 图标大小对应原组件 h-4 w-4 (16px)
    :deep(.el-icon) {
      font-size: 16px;
    }
  }
  
  &__title-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  &__title {
    // 对应原组件 text-2xl font-bold tracking-tight
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.25;
    margin: 0;
    color: var(--el-text-color-primary);
  }
  
  &__description {
    // 对应原组件 text-muted-foreground
    margin: 0;
    font-size: 14px;
    color: var(--el-text-color-regular);
    line-height: 1.5;
  }
  
  &__right {
    display: flex;
    align-items: center;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    &__left {
      width: 100%;
    }
    
    &__right {
      width: 100%;
      justify-content: flex-end;
    }
    
    &__title {
      font-size: 20px;
    }
  }
}

// 暗色主题适配
.dark {
  .page-header {
    &__title {
      color: var(--el-text-color-primary);
    }
    
    &__description {
      color: var(--el-text-color-regular);
    }
  }
}
</style>