import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 制造商表单验证规则 */
const formRules = reactive(<FormRules>{
  manufacturerName: [
    { required: true, message: "制造商名称为必填项", trigger: "blur" }
  ]
});

/** 制造商联系人表单验证规则 */
const contactFormRules = reactive(<FormRules>{
  contactName: [
    { required: true, message: "联系人姓名为必填项", trigger: "blur" }
  ],
  contactPhone: [
    { 
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码格式",
      trigger: "blur"
    }
  ],
  contactEmail: [
    {
      type: "email",
      message: "请输入正确的邮箱格式",
      trigger: "blur"
    }
  ]
});

export { formRules, contactFormRules };