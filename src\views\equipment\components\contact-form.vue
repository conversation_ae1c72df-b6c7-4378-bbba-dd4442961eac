<script setup lang="ts">
/**
 * 联系人表单组件
 * 从React设备管理系统迁移而来的联系人信息录入表单
 * 与原React组件功能完全对等，支持制造商/供应商关联类型动态切换
 */
import { reactive, ref, computed, watch } from "vue";
import ReCol from "@/components/ReCol";

defineOptions({
  name: "EquipmentContactForm"
});

// 制造商数据接口
interface Manufacturer {
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  remarks?: string;
  enabled?: boolean;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 供应商数据接口
interface Supplier {
  equipmentSupplierId?: number;
  supplierName?: string;
  supplierAddress?: string;
  remarks?: string;
  enabled?: boolean;
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 联系人数据接口
interface Contact {
  equipmentContactId?: number;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  entityType?: "manufacturer" | "supplier";
  entityId?: number;
  entityName?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 表单数据接口
interface ContactFormData {
  contactName: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  entityType: "manufacturer" | "supplier";
  entityId: number;
}

// 组件Props接口
interface ContactFormProps {
  /** 初始数据（编辑模式时传入） */
  initialData?: Contact;
  /** 制造商列表 */
  manufacturers: Manufacturer[];
  /** 供应商列表 */
  suppliers: Supplier[];
  /** 表单提交回调 */
  onSubmit: (data: ContactFormData) => void;
  /** 取消操作回调 */
  onCancel: () => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
}

const props = withDefaults(defineProps<ContactFormProps>(), {
  initialData: undefined,
  manufacturers: () => [],
  suppliers: () => [],
  isLoading: false
});

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive<ContactFormData>({
  contactName: props.initialData?.contactName || "",
  contactPhone: props.initialData?.contactPhone || "",
  contactEmail: props.initialData?.contactEmail || "",
  contactRole: props.initialData?.contactRole || "",
  entityType: props.initialData?.entityType || "manufacturer",
  entityId: props.initialData?.entityId || 0
});

// 手机号码验证规则
const phoneValidator = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    callback();
  }
};

// 邮箱验证规则
const emailValidator = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    callback(new Error("请输入正确的邮箱地址"));
  } else {
    callback();
  }
};

// 表单验证规则
const formRules = {
  contactName: [
    { required: true, message: "联系人姓名不能为空", trigger: "blur" },
    { max: 50, message: "联系人姓名不能超过50个字符", trigger: "blur" }
  ],
  contactPhone: [
    { validator: phoneValidator, trigger: "blur" }
  ],
  contactEmail: [
    { validator: emailValidator, trigger: "blur" }
  ],
  contactRole: [
    { max: 50, message: "职位不能超过50个字符", trigger: "blur" }
  ],
  entityType: [
    { required: true, message: "请选择关联类型", trigger: "change" }
  ],
  entityId: [
    { 
      type: "number", 
      min: 1, 
      message: "请选择关联实体", 
      trigger: "change" 
    }
  ]
};

// 根据实体类型过滤可用选项
const entityOptions = computed(() => {
  if (formData.entityType === "manufacturer") {
    return props.manufacturers
      .filter(m => m.enabled)
      .map(m => ({
        value: m.equipmentManufacturerId!,
        label: m.manufacturerName!
      }));
  } else {
    return props.suppliers
      .filter(s => s.enabled)
      .map(s => ({
        value: s.equipmentSupplierId!,
        label: s.supplierName!
      }));
  }
});

// 监听实体类型变化，重置实体ID
watch(
  () => formData.entityType,
  (newType, oldType) => {
    if (newType !== oldType) {
      formData.entityId = 0;
      // 清除验证错误
      formRef.value?.clearValidate('entityId');
    }
  }
);

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      props.onSubmit({
        contactName: formData.contactName,
        contactPhone: formData.contactPhone,
        contactEmail: formData.contactEmail,
        contactRole: formData.contactRole,
        entityType: formData.entityType,
        entityId: formData.entityId
      });
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  // 恢复初始数据
  formData.contactName = props.initialData?.contactName || "";
  formData.contactPhone = props.initialData?.contactPhone || "";
  formData.contactEmail = props.initialData?.contactEmail || "";
  formData.contactRole = props.initialData?.contactRole || "";
  formData.entityType = props.initialData?.entityType || "manufacturer";
  formData.entityId = props.initialData?.entityId || 0;
};

// 暴露方法给父组件
const getRef = () => {
  return formRef.value;
};

defineExpose({ 
  getRef,
  resetForm,
  handleSubmit
});
</script>

<template>
  <div class="contact-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="top"
    >
      <!-- 联系人姓名 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              v-model="formData.contactName"
              placeholder="请输入联系人姓名"
              clearable
              :disabled="isLoading"
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 联系电话和邮箱地址 -->
      <el-row :gutter="20">
        <re-col :value="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input
              v-model="formData.contactPhone"
              placeholder="请输入联系电话"
              clearable
              :disabled="isLoading"
            />
          </el-form-item>
        </re-col>
        <re-col :value="12">
          <el-form-item label="邮箱地址" prop="contactEmail">
            <el-input
              v-model="formData.contactEmail"
              placeholder="请输入邮箱地址"
              clearable
              :disabled="isLoading"
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 职位 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="职位" prop="contactRole">
            <el-input
              v-model="formData.contactRole"
              placeholder="请输入职位"
              clearable
              :disabled="isLoading"
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 关联类型和关联实体 -->
      <el-row :gutter="20">
        <re-col :value="12">
          <el-form-item label="关联类型" prop="entityType">
            <el-select
              v-model="formData.entityType"
              placeholder="请选择关联类型"
              :disabled="isLoading"
              style="width: 100%"
            >
              <el-option
                label="制造商"
                value="manufacturer"
              />
              <el-option
                label="供应商"
                value="supplier"
              />
            </el-select>
          </el-form-item>
        </re-col>
        <re-col :value="12">
          <el-form-item label="关联实体" prop="entityId">
            <el-select
              v-model="formData.entityId"
              placeholder="请选择关联实体"
              clearable
              filterable
              :disabled="isLoading"
              style="width: 100%"
            >
              <el-option
                v-for="option in entityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <div v-if="entityOptions.length === 0" class="entity-empty-tip">
              <el-text type="warning" size="small">
                暂无可用{{ formData.entityType === 'manufacturer' ? '制造商' : '供应商' }}，请先添加相关数据
              </el-text>
            </div>
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <div class="form-actions">
            <el-button
              :disabled="isLoading"
              @click="onCancel"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              :loading="isLoading"
              @click="handleSubmit"
            >
              {{ isLoading ? "保存中..." : "保存" }}
            </el-button>
          </div>
        </re-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.contact-form {
  padding: 20px;
  
  // 实体空提示样式
  .entity-empty-tip {
    margin-top: 4px;
  }
  
  // 表单操作按钮区域 - 对应原组件的右对齐布局
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 24px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .contact-form {
    padding: 16px;
    
    .form-actions {
      flex-direction: column-reverse;
      
      .el-button {
        width: 100%;
      }
    }
  }
}

// 暗色主题适配
.dark {
  .contact-form {
    // 暗色主题下的样式调整
  }
}
</style>