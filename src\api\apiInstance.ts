import { Api } from "./Api";
import { httpExecutor } from "./httpExecutor";

/**
 * 🚀 全局API实例
 *
 * 使用新的Controller架构 + 现有http工具的完美结合：
 * - 🎯 类型安全的API调用
 * - 🔒 保持所有拦截器功能
 * - 🚦 token管理和错误处理
 * - 📊 进度条和错误提示
 */
export const api = new Api(httpExecutor);

/**
 * 📦 导出所有Controller实例以便按需使用
 *
 * 使用方式：
 * ```typescript
 * import { api } from "@/api/apiInstance";
 *
 * // 分页查询公司
 * const result = await api.companyQueryController.getCompanyPage({
 *   body: spec,
 *   index: 1,
 *   size: 10
 * });
 * ```
 */
export const {
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  webSocketStompController,
  signController,
  equipmentManufacturerCmdController,
  equipmentManufacturerContactCmdController,
  equipmentSupplierCmdController,
  equipmentSupplierContactCmdController,
  equipmentTypeCmdController,
  equipmentManufacturerContactQueryController,
  equipmentManufacturerQueryController,
  equipmentSupplierContactQueryController,
  equipmentSupplierQueryController,
  equipmentTypeQueryController,
  deleteEmployeeController,
  dissolveCompanyController,
  dissolveDeptController,
  dissolvePositionController,
  editCompanyInfoController,
  editDeptController,
  editEmployeeInfoController,
  editPositionInfoController,
  employeeOnboardingController,
  employeeResignationController,
  establishCompanyController,
  establishDeptController,
  establishPositionController,
  companyQueryController,
  deptQueryController,
  employeeQueryController,
  positionQueryController,
  dictController,
  menuCmdController,
  menuQueryController,
  roleCmdController,
  roleQueryController,
  userCmdController,
  tenantQueryController,
  userQueryController,



















} = api;

// 🎯 默认导出api实例，方便直接使用
export default api;
