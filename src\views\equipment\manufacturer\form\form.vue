<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import ReCol from "@/components/ReCol";
import { formRules } from "../utils/rule";
import { FormProps } from "../utils/types";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    equipmentManufacturerId: null,
    manufacturerName: "",
    remarks: "",
    enabled: true
  })
});

const ruleFormRef = ref();
const newFormInline = reactive(props.formInline);

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="120px"
  >
    <el-row :gutter="30">
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="制造商名称" prop="manufacturerName">
          <el-input
            v-model="newFormInline.manufacturerName"
            clearable
            placeholder="请输入制造商名称"
          />
        </el-form-item>
      </re-col>
      <re-col :value="12" :xs="24" :sm="24">
        <el-form-item label="状态">
          <el-switch
            v-model="newFormInline.enabled"
            inline-prompt
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </re-col>

      <re-col>
        <el-form-item label="备注">
          <el-input
            v-model="newFormInline.remarks"
            type="textarea"
            :rows="3"
            clearable
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </re-col>
    </el-row>
  </el-form>
</template>