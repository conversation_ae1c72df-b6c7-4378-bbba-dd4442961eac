<script setup lang="ts">
/**
 * DataTable组件使用示例
 * 展示如何在项目中使用迁移后的数据表格组件
 */
import { ref, h } from "vue";
import DataTable from "./data-table.vue";
import StatusBadge from "./status-badge.vue";

defineOptions({
  name: "DataTableUsageExample"
});

// 模拟数据类型
interface Manufacturer {
  id: number;
  manufacturerName: string;
  remarks: string;
  enabled: boolean;
  createTime: string;
  contactCount: number;
}

interface Supplier {
  id: number;
  supplierName: string;
  supplierAddress: string;
  manufacturerName: string;
  enabled: boolean;
  createTime: string;
}

// 模拟制造商数据
const manufacturerData = ref<Manufacturer[]>([
  {
    id: 1,
    manufacturerName: "华为技术有限公司",
    remarks: "全球领先的ICT基础设施和智能终端提供商",
    enabled: true,
    createTime: "2023-01-15 10:30:00",
    contactCount: 3
  },
  {
    id: 2,
    manufacturerName: "小米科技有限公司",
    remarks: "专注于智能硬件和电子产品研发",
    enabled: false,
    createTime: "2023-02-20 14:20:00",
    contactCount: 2
  },
  {
    id: 3,
    manufacturerName: "比亚迪股份有限公司",
    remarks: "新能源汽车及动力电池制造商",
    enabled: true,
    createTime: "2023-03-10 09:15:00",
    contactCount: 5
  }
]);

// 模拟供应商数据
const supplierData = ref<Supplier[]>([
  {
    id: 1,
    supplierName: "深圳市创新供应链有限公司",
    supplierAddress: "深圳市南山区科技园",
    manufacturerName: "华为技术有限公司",
    enabled: true,
    createTime: "2023-01-20 11:00:00"
  },
  {
    id: 2,
    supplierName: "上海智能物流有限公司",
    supplierAddress: "上海市浦东新区张江高科技园区",
    manufacturerName: "小米科技有限公司",
    enabled: false,
    createTime: "2023-02-25 16:30:00"
  }
]);

// 制造商表格列配置
const manufacturerColumns = [
  {
    key: "manufacturerName",
    title: "制造商名称",
    width: 200,
    showOverflowTooltip: true
  },
  {
    key: "remarks",
    title: "备注",
    minWidth: 300,
    showOverflowTooltip: true
  },
  {
    key: "enabled",
    title: "状态",
    width: 100,
    align: "center" as const,
    render: (value: boolean) => h(StatusBadge, { enabled: value })
  },
  {
    key: "contactCount",
    title: "联系人数量",
    width: 120,
    align: "center" as const,
    render: (value: number) => h("span", { class: "contact-count" }, `${value}人`)
  },
  {
    key: "createTime",
    title: "创建时间",
    width: 180
  }
];

// 供应商表格列配置
const supplierColumns = [
  {
    key: "supplierName",
    title: "供应商名称",
    width: 200,
    showOverflowTooltip: true
  },
  {
    key: "supplierAddress",
    title: "地址",
    minWidth: 250,
    showOverflowTooltip: true
  },
  {
    key: "manufacturerName",
    title: "关联制造商",
    width: 180,
    showOverflowTooltip: true
  },
  {
    key: "enabled",
    title: "状态",
    width: 100,
    align: "center" as const,
    render: (value: boolean) => h(StatusBadge, { enabled: value })
  },
  {
    key: "createTime",
    title: "创建时间",
    width: 180
  }
];

// 操作回调函数
const handleView = (record: any) => {
  console.log("查看:", record);
  ElMessage.info(`查看 ${record.manufacturerName || record.supplierName}`);
};

const handleEdit = (record: any) => {
  console.log("编辑:", record);
  ElMessage.info(`编辑 ${record.manufacturerName || record.supplierName}`);
};

const handleDelete = (record: any) => {
  console.log("删除:", record);
  ElMessageBox.confirm(
    `确认删除 ${record.manufacturerName || record.supplierName} 吗？`,
    "删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    ElMessage.success("删除成功");
  }).catch(() => {
    ElMessage.info("已取消删除");
  });
};

const handleManageContacts = (record: any) => {
  console.log("管理联系人:", record);
  ElMessage.info(`管理 ${record.manufacturerName || record.supplierName} 的联系人`);
};

// 测试数据切换
const currentDataType = ref<"manufacturer" | "supplier">("manufacturer");
const switchDataType = (type: "manufacturer" | "supplier") => {
  currentDataType.value = type;
};
</script>

<template>
  <div class="data-table-usage-example">
    <el-card header="DataTable组件使用示例">
      
      <!-- 数据类型切换 -->
      <el-divider>数据类型切换</el-divider>
      <div class="data-type-switch">
        <el-radio-group v-model="currentDataType" @change="switchDataType">
          <el-radio-button value="manufacturer">制造商数据</el-radio-button>
          <el-radio-button value="supplier">供应商数据</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 制造商表格示例 -->
      <el-divider>制造商管理表格</el-divider>
      <div v-show="currentDataType === 'manufacturer'" class="example-section">
        <DataTable
          :data="manufacturerData"
          :columns="manufacturerColumns"
          :on-view="handleView"
          :on-edit="handleEdit"
          :on-delete="handleDelete"
          :on-manage-contacts="handleManageContacts"
          border
          stripe
          row-key="id"
        />
      </div>

      <!-- 供应商表格示例 -->
      <div v-show="currentDataType === 'supplier'" class="example-section">
        <DataTable
          :data="supplierData"
          :columns="supplierColumns"
          :on-view="handleView"
          :on-edit="handleEdit"
          :on-delete="handleDelete"
          border
          stripe
          row-key="id"
        />
      </div>

      <!-- 基本使用示例 -->
      <el-divider>基本使用（仅数据展示）</el-divider>
      <div class="example-section">
        <DataTable
          :data="manufacturerData.slice(0, 2)"
          :columns="manufacturerColumns.slice(0, 3)"
          :border="false"
          :stripe="false"
        />
      </div>

      <!-- 加载状态示例 -->
      <el-divider>加载状态</el-divider>
      <div class="example-section">
        <DataTable
          :data="[]"
          :columns="manufacturerColumns.slice(0, 3)"
          loading
        />
      </div>

      <!-- 空数据状态示例 -->
      <el-divider>空数据状态</el-divider>
      <div class="example-section">
        <DataTable
          :data="[]"
          :columns="manufacturerColumns.slice(0, 3)"
        />
      </div>

      <!-- 功能对比说明 -->
      <el-divider>功能对比</el-divider>
      <div class="comparison-section">
        <el-descriptions title="React vs Vue3 功能对比" :column="1" border>
          <el-descriptions-item label="组件名称">
            <el-tag type="info">React: DataTable&lt;T&gt;</el-tag>
            <el-tag type="success">Vue3: EquipmentDataTable&lt;T&gt;</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="泛型支持">
            <div>✅ 完全支持TypeScript泛型</div>
          </el-descriptions-item>
          <el-descriptions-item label="列配置">
            <div>
              <div>• key: 字段名</div>
              <div>• title: 列标题</div>
              <div>• render?: 自定义渲染函数</div>
              <div>• width/minWidth: 列宽控制</div>
              <div>• align: 对齐方式</div>
              <div>• fixed: 固定列</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="操作菜单">
            <div>✅ 查看、编辑、删除、管理联系人</div>
          </el-descriptions-item>
          <el-descriptions-item label="空数据状态">
            <div>✅ 显示"暂无数据"提示</div>
          </el-descriptions-item>
          <el-descriptions-item label="基础组件">
            <el-tag type="info">React: shadcn/ui Table</el-tag>
            <el-tag type="success">Vue3: Element Plus Table</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="增强功能">
            <div>
              <div>✅ 边框、斑马纹控制</div>
              <div>✅ 表格尺寸控制</div>
              <div>✅ 加载状态</div>
              <div>✅ 固定列支持</div>
              <div>✅ 文字溢出提示</div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 代码示例 -->
      <el-divider>代码示例</el-divider>
      <div class="code-example-section">
        <el-collapse>
          <el-collapse-item title="基本使用代码" name="basic">
            <pre><code>{{ basicUsageCode }}</code></pre>
          </el-collapse-item>
          <el-collapse-item title="完整功能代码" name="full">
            <pre><code>{{ fullUsageCode }}</code></pre>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
// 代码示例
const basicUsageCode = `<script setup lang="ts">
import DataTable from "@/views/equipment/components/data-table.vue";

interface MyData {
  id: number;
  name: string;
  status: boolean;
}

const data = ref<MyData[]>([...]);
const columns = [
  { key: "name", title: "名称" },
  { key: "status", title: "状态", render: (value) => value ? "启用" : "禁用" }
];
</script>

<template>
  <DataTable :data="data" :columns="columns" />
</template>`;

const fullUsageCode = `<script setup lang="ts">
import DataTable from "@/views/equipment/components/data-table.vue";
import StatusBadge from "@/views/equipment/components/status-badge.vue";

const columns = [
  { key: "name", title: "名称", width: 200 },
  { 
    key: "status", 
    title: "状态", 
    width: 100,
    render: (value) => h(StatusBadge, { enabled: value })
  }
];

const handleEdit = (record) => {
  // 编辑逻辑
};
</script>

<template>
  <DataTable 
    :data="data" 
    :columns="columns"
    :on-view="handleView"
    :on-edit="handleEdit"
    :on-delete="handleDelete"
    border
    stripe
  />
</template>`;
</script>

<style lang="scss" scoped>
.data-table-usage-example {
  padding: 20px;
}

.data-type-switch {
  margin: 16px 0;
}

.example-section {
  margin: 16px 0;
}

.comparison-section {
  margin-top: 16px;
}

.code-example-section {
  margin-top: 16px;
  
  pre {
    background-color: var(--el-fill-color-lighter);
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    
    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

// 自定义样式
:deep(.contact-count) {
  color: var(--el-color-primary);
  font-weight: 500;
}

// 暗色主题适配
.dark {
  .code-example-section {
    pre {
      background-color: var(--el-fill-color-darker);
    }
  }
}
</style>