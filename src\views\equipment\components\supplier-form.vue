<script setup lang="ts">
/**
 * 供应商表单组件
 * 从React设备管理系统迁移而来的供应商信息录入表单
 * 与原React组件功能完全对等，支持新增和编辑模式，包含制造商关联功能
 */
import { reactive, ref, computed } from "vue";
import ReCol from "@/components/ReCol";

defineOptions({
  name: "EquipmentSupplierForm"
});

// 制造商数据接口
interface Manufacturer {
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  remarks?: string;
  enabled?: boolean;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 供应商数据接口
interface Supplier {
  equipmentSupplierId?: number;
  supplierName?: string;
  supplierAddress?: string;
  remarks?: string;
  enabled?: boolean;
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 表单数据接口
interface SupplierFormData {
  supplierName: string;
  supplierAddress?: string;
  remarks?: string;
  enabled?: boolean;
  equipmentManufacturerId: number;
}

// 组件Props接口
interface SupplierFormProps {
  /** 初始数据（编辑模式时传入） */
  initialData?: Supplier;
  /** 制造商列表 */
  manufacturers: Manufacturer[];
  /** 表单提交回调 */
  onSubmit: (data: SupplierFormData) => void;
  /** 取消操作回调 */
  onCancel: () => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
}

const props = withDefaults(defineProps<SupplierFormProps>(), {
  initialData: undefined,
  manufacturers: () => [],
  isLoading: false
});

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive<SupplierFormData>({
  supplierName: props.initialData?.supplierName || "",
  supplierAddress: props.initialData?.supplierAddress || "",
  remarks: props.initialData?.remarks || "",
  enabled: props.initialData?.enabled ?? true,
  equipmentManufacturerId: props.initialData?.equipmentManufacturerId || 0
});

// 表单验证规则
const formRules = {
  supplierName: [
    { required: true, message: "供应商名称不能为空", trigger: "blur" },
    { max: 100, message: "供应商名称不能超过100个字符", trigger: "blur" }
  ],
  supplierAddress: [
    { max: 200, message: "地址不能超过200个字符", trigger: "blur" }
  ],
  remarks: [
    { max: 500, message: "备注不能超过500个字符", trigger: "blur" }
  ],
  equipmentManufacturerId: [
    { 
      type: "number", 
      min: 1, 
      message: "请选择关联制造商", 
      trigger: "change" 
    }
  ]
};

// 过滤已启用的制造商选项
const enabledManufacturers = computed(() => {
  return props.manufacturers
    .filter(m => m.enabled)
    .map(m => ({
      value: m.equipmentManufacturerId!,
      label: m.manufacturerName!
    }));
});

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      props.onSubmit({
        supplierName: formData.supplierName,
        supplierAddress: formData.supplierAddress,
        remarks: formData.remarks,
        enabled: formData.enabled,
        equipmentManufacturerId: formData.equipmentManufacturerId
      });
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  // 恢复初始数据
  formData.supplierName = props.initialData?.supplierName || "";
  formData.supplierAddress = props.initialData?.supplierAddress || "";
  formData.remarks = props.initialData?.remarks || "";
  formData.enabled = props.initialData?.enabled ?? true;
  formData.equipmentManufacturerId = props.initialData?.equipmentManufacturerId || 0;
};

// 暴露方法给父组件
const getRef = () => {
  return formRef.value;
};

defineExpose({ 
  getRef,
  resetForm,
  handleSubmit
});
</script>

<template>
  <div class="supplier-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="top"
    >
      <!-- 供应商名称 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="供应商名称" prop="supplierName">
            <el-input
              v-model="formData.supplierName"
              placeholder="请输入供应商名称"
              clearable
              :disabled="isLoading"
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 供应商地址 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="供应商地址" prop="supplierAddress">
            <el-input
              v-model="formData.supplierAddress"
              placeholder="请输入供应商地址"
              clearable
              :disabled="isLoading"
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 关联制造商 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="关联制造商" prop="equipmentManufacturerId">
            <el-select
              v-model="formData.equipmentManufacturerId"
              placeholder="请选择关联制造商"
              clearable
              filterable
              :disabled="isLoading"
              style="width: 100%"
            >
              <el-option
                v-for="manufacturer in enabledManufacturers"
                :key="manufacturer.value"
                :label="manufacturer.label"
                :value="manufacturer.value"
              />
            </el-select>
            <div v-if="enabledManufacturers.length === 0" class="manufacturer-empty-tip">
              <el-text type="warning" size="small">
                暂无可用制造商，请先添加制造商
              </el-text>
            </div>
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 备注信息 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              :disabled="isLoading"
              resize="none"
              clearable
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 启用状态 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="启用状态">
            <div class="status-switch-container">
              <div class="status-info">
                <div class="status-label">启用状态</div>
                <div class="status-description">启用后该供应商将可以被选择和使用</div>
              </div>
              <el-switch
                v-model="formData.enabled"
                :disabled="isLoading"
                inline-prompt
                active-text="启用"
                inactive-text="禁用"
              />
            </div>
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <div class="form-actions">
            <el-button
              :disabled="isLoading"
              @click="onCancel"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              :loading="isLoading"
              @click="handleSubmit"
            >
              {{ isLoading ? "保存中..." : "保存" }}
            </el-button>
          </div>
        </re-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.supplier-form {
  padding: 20px;
  
  // 制造商空提示样式
  .manufacturer-empty-tip {
    margin-top: 4px;
  }
  
  // 状态开关容器样式 - 对应原组件的flex布局
  .status-switch-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    background-color: var(--el-bg-color-page);
    
    .status-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .status-label {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
      
      .status-description {
        font-size: 14px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
    }
  }
  
  // 表单操作按钮区域 - 对应原组件的右对齐布局
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 24px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .supplier-form {
    padding: 16px;
    
    .status-switch-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .status-info {
        width: 100%;
      }
    }
    
    .form-actions {
      flex-direction: column-reverse;
      
      .el-button {
        width: 100%;
      }
    }
  }
}

// 暗色主题适配
.dark {
  .supplier-form {
    .status-switch-container {
      background-color: var(--el-bg-color-page);
      border-color: var(--el-border-color);
    }
  }
}
</style>