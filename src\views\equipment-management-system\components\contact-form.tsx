"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import type { ContactFormData, Contact, Manufacturer, Supplier } from "@/types";

const formSchema = z.object({
  contact_name: z
    .string()
    .min(1, "联系人姓名不能为空")
    .max(50, "联系人姓名不能超过50个字符"),
  contact_phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, "请输入正确的手机号码")
    .optional()
    .or(z.literal("")),
  contact_email: z
    .string()
    .email("请输入正确的邮箱地址")
    .optional()
    .or(z.literal("")),
  contact_role: z.string().max(50, "职位不能超过50个字符").optional(),
  entity_type: z.enum(["manufacturer", "supplier"]),
  entity_id: z.number().min(1, "请选择关联实体")
});

interface ContactFormProps {
  initialData?: Contact;
  manufacturers: Manufacturer[];
  suppliers: Supplier[];
  onSubmit: (data: ContactFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function ContactForm({
  initialData,
  manufacturers,
  suppliers,
  onSubmit,
  onCancel,
  isLoading = false
}: ContactFormProps) {
  const form = useForm<ContactFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      contact_name: initialData?.contact_name || "",
      contact_phone: initialData?.contact_phone || "",
      contact_email: initialData?.contact_email || "",
      contact_role: initialData?.contact_role || "",
      entity_type: initialData?.entity_type || "manufacturer",
      entity_id: initialData?.entity_id || 0
    }
  });

  const entityType = form.watch("entity_type");

  const handleSubmit = (data: ContactFormData) => {
    onSubmit(data);
  };

  const getEntityOptions = () => {
    if (entityType === "manufacturer") {
      return manufacturers
        .filter(m => m.enabled)
        .map(m => ({
          value: m.equipment_manufacturer_id,
          label: m.manufacturer_name
        }));
    } else {
      return suppliers
        .filter(s => s.enabled)
        .map(s => ({
          value: s.equipment_supplier_id,
          label: s.supplier_name
        }));
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="contact_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系人姓名 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系人姓名" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="contact_phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>联系电话</FormLabel>
                <FormControl>
                  <Input placeholder="请输入联系电话" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contact_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>邮箱地址</FormLabel>
                <FormControl>
                  <Input placeholder="请输入邮箱地址" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="contact_role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>职位</FormLabel>
              <FormControl>
                <Input placeholder="请输入职位" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="entity_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>关联类型 *</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择关联类型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="manufacturer">制造商</SelectItem>
                    <SelectItem value="supplier">供应商</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="entity_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>关联实体 *</FormLabel>
                <Select
                  onValueChange={value =>
                    field.onChange(Number.parseInt(value))
                  }
                  value={field.value ? String(field.value) : ""}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择关联实体" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {getEntityOptions().map(option => (
                      <SelectItem
                        key={option.value}
                        value={String(option.value)}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "保存中..." : "保存"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
