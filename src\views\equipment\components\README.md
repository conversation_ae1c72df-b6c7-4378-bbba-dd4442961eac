# Equipment Components 组件迁移指南

## 📋 组件概述

本目录包含从React设备管理系统迁移而来的Vue3组件，所有组件都遵循项目代码规范，保持功能完全对等。

### 已迁移组件

1. **`status-badge.vue`** - 状态徽章组件，用于显示设备相关实体的启用/禁用状态
2. **`page-header.vue`** - 页面头部组件，用于显示页面标题、描述信息，提供返回和新增等操作功能
3. **`data-table.vue`** - 通用数据表格组件，支持泛型、自定义渲染、操作菜单等功能
4. **`manufacturer-form.vue`** - 制造商表单组件，用于制造商信息的录入和编辑功能
5. **`supplier-form.vue`** - 供应商表单组件，用于供应商信息的录入和编辑功能，支持制造商关联选择

## 🎯 Status Badge 组件

`status-badge.vue` 是从React设备管理系统迁移而来的状态徽章组件，用于显示设备相关实体的启用/禁用状态。

## 🎯 功能特性

### 原React组件特性
- ✅ 显示启用/禁用状态
- ✅ 支持自定义CSS类名
- ✅ 绿色(启用)/灰色(禁用)配色方案
- ✅ 基于shadcn/ui Badge组件

### Vue3迁移后特性
- ✅ 完全对等的功能实现
- ✅ 使用Element Plus Tag组件
- ✅ 保持一致的视觉效果
- ✅ 完整的TypeScript类型支持
- ✅ 遵循Vue3项目代码规范

## 📦 使用方法

### 基本使用

```vue
<script setup lang="ts">
import StatusBadge from "@/views/equipment/components/status-badge.vue";
</script>

<template>
  <!-- 显示启用状态 -->
  <StatusBadge :enabled="true" />
  
  <!-- 显示禁用状态 -->
  <StatusBadge :enabled="false" />
  
  <!-- 带自定义CSS类 -->
  <StatusBadge :enabled="true" class-name="my-custom-class" />
</template>
```

### 在表格中使用

#### 方法1: 模板语法 (推荐)
```vue
<el-table-column prop="enabled" label="状态" width="100">
  <template #default="{ row }">
    <StatusBadge :enabled="row.enabled" />
  </template>
</el-table-column>
```

#### 方法2: cellRenderer (高级用法)
```typescript
// 在hook.tsx中的columns配置
{
  label: "状态",
  prop: "enabled",
  minWidth: 80,
  cellRenderer: ({ row }) => (
    <StatusBadge enabled={row.enabled} />
  )
}
```

## 🔄 迁移现有代码

### 替换现有的状态显示

**原有代码 (需要替换):**
```typescript
// 在 hook.tsx 中
cellRenderer: ({ row }) => (
  <el-tag type={row.enabled ? "success" : "danger"}>
    {row.enabled ? "启用" : "禁用"}
  </el-tag>
)
```

**新代码 (推荐):**
```typescript
// 1. 在hook.tsx顶部导入组件
import StatusBadge from "../components/status-badge.vue";

// 2. 更新cellRenderer
cellRenderer: ({ row }) => (
  <StatusBadge enabled={row.enabled} />
)
```

### 具体迁移步骤

1. **导入组件**
   ```typescript
   import StatusBadge from "@/views/equipment/components/status-badge.vue";
   ```

2. **更新表格列配置**
   ```typescript
   // 将现有的el-tag替换为StatusBadge
   {
     label: "状态",
     prop: "enabled",
     minWidth: 80,
     cellRenderer: ({ row }) => <StatusBadge enabled={row.enabled} />
   }
   ```

3. **验证功能**
   - 确保状态显示正确
   - 检查样式是否一致
   - 测试自定义className功能

## 🎨 样式定制

### 自定义样式类
```scss
// 在父组件中添加自定义样式
:deep(.my-custom-badge) {
  font-weight: bold;
  margin: 0 4px;
}
```

### 颜色映射对照表
| 状态 | React原始 | Vue3实现 | 颜色值 |
|------|-----------|----------|--------|
| 启用 | `bg-green-100 text-green-800` | Element Plus success | `#dcfce7 / #166534` |
| 禁用 | `bg-gray-100 text-gray-800` | Element Plus info | `#f3f4f6 / #374151` |

## 📝 Props API

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | `boolean` | `true` | 是否为启用状态 |
| className | `string?` | `""` | 自定义CSS类名 |

## 🧪 测试用例

```vue
<script setup lang="ts">
import StatusBadge from "./status-badge.vue";

// 测试数据
const testCases = [
  { enabled: true, expected: "启用", color: "green" },
  { enabled: false, expected: "禁用", color: "gray" }
];
</script>

<template>
  <div v-for="test in testCases" :key="test.expected">
    <StatusBadge :enabled="test.enabled" />
    <span>期望: {{ test.expected }}</span>
  </div>
</template>
```

## 🚀 最佳实践

1. **一致性**: 在所有需要显示状态的地方使用此组件
2. **性能**: 在大表格中使用时考虑虚拟滚动
3. **可访问性**: 组件已内置ARIA支持
4. **主题**: 样式会自动适配Element Plus主题

## 📋 迁移检查清单

- [ ] 导入StatusBadge组件
- [ ] 替换现有的状态显示逻辑
- [ ] 验证功能对等性
- [ ] 检查样式一致性
- [ ] 测试自定义className
- [ ] 更新相关文档

---

## 🎯 Data Table 组件

`data-table.vue` 是从React设备管理系统迁移而来的通用数据表格组件，支持泛型、自定义渲染、操作菜单等功能。

### 功能特性：
- ✅ 完全的TypeScript泛型支持
- ✅ 灵活的列配置（自定义渲染、宽度、对齐、固定列等）
- ✅ 内置操作菜单（查看、编辑、删除、管理联系人）
- ✅ 空数据状态处理
- ✅ 加载状态支持
- ✅ 边框、斑马纹、大小控制
- ✅ 明暗主题适配

### 基本使用：
```vue
<script setup lang="ts">
import DataTable from "@/views/equipment/components/data-table.vue";
import { h } from "vue";

interface MyData {
  id: number;
  name: string;
  status: boolean;
}

const data = ref<MyData[]>([...]);
const columns = [
  { key: "name", title: "名称", width: 200 },
  { 
    key: "status", 
    title: "状态", 
    render: (value) => h(StatusBadge, { enabled: value })
  }
];

const handleEdit = (record: MyData) => {
  // 编辑逻辑
};
</script>

<template>
  <DataTable 
    :data="data"
    :columns="columns"
    :on-edit="handleEdit"
    :on-delete="handleDelete"
    border
    stripe
  />
</template>
```

---

## 🎯 Manufacturer Form 组件

`manufacturer-form.vue` 是从React设备管理系统迁移而来的制造商表单组件，用于制造商信息的录入和编辑功能。

### 功能特性：
- ✅ 制造商名称输入（必填，1-100字符验证）
- ✅ 备注信息输入（可选，最多500字符）
- ✅ 启用状态切换（默认启用）
- ✅ 表单验证和错误提示
- ✅ 新增/编辑模式支持
- ✅ 加载状态和禁用支持
- ✅ 响应式布局适配
- ✅ 明暗主题支持

### 基本使用：
```vue
<script setup lang="ts">
import ManufacturerForm from "@/views/equipment/components/manufacturer-form.vue";

const handleSubmit = (data: ManufacturerFormData) => {
  // 处理表单提交
  console.log("提交数据:", data);
};

const handleCancel = () => {
  // 处理取消操作
  console.log("取消操作");
};

// 编辑模式数据
const editData = {
  manufacturerName: "华为技术有限公司",
  remarks: "全球领先的ICT基础设施和智能终端提供商",
  enabled: true
};
</script>

<template>
  <!-- 新增模式 -->
  <ManufacturerForm
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
  
  <!-- 编辑模式 -->
  <ManufacturerForm
    :initial-data="editData"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

---

## 🎯 Supplier Form 组件

`supplier-form.vue` 是从React设备管理系统迁移而来的供应商表单组件，用于供应商信息的录入和编辑功能，包含制造商关联选择功能。

### 功能特性：
- ✅ 供应商名称输入（必填，1-100字符验证）
- ✅ 供应商地址输入（可选，最多200字符）
- ✅ 关联制造商选择（必填，下拉选择）
- ✅ 备注信息输入（可选，最多500字符）
- ✅ 启用状态切换（默认启用）
- ✅ 制造商过滤（只显示已启用的制造商）
- ✅ 表单验证和错误提示
- ✅ 新增/编辑模式支持
- ✅ 加载状态和禁用支持
- ✅ 响应式布局适配
- ✅ 明暗主题支持
- 🎉 **增强功能**：制造商空状态提示
- 🎉 **增强功能**：下拉搜索过滤功能
- 🎉 **增强功能**：表单方法暴露

### 基本使用：
```vue
<script setup lang="ts">
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

// 制造商列表（通常从API获取）
const manufacturerList = ref<Manufacturer[]>([
  { 
    equipmentManufacturerId: 1, 
    manufacturerName: "华为技术有限公司", 
    enabled: true 
  },
  { 
    equipmentManufacturerId: 2, 
    manufacturerName: "小米科技有限公司", 
    enabled: true 
  }
]);

const handleSubmit = (data: SupplierFormData) => {
  // 处理表单提交
  console.log("提交数据:", data);
};

const handleCancel = () => {
  // 处理取消操作
  console.log("取消操作");
};

// 编辑模式数据
const editData = {
  equipmentSupplierId: 1,
  supplierName: "深圳市创新供应链有限公司",
  supplierAddress: "深圳市南山区科技园",
  equipmentManufacturerId: 1,
  remarks: "专业的供应链服务商",
  enabled: true
};
</script>

<template>
  <!-- 新增模式 -->
  <SupplierForm
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
  
  <!-- 编辑模式 -->
  <SupplierForm
    :initial-data="editData"
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

### 在对话框中使用：
```vue
<script setup lang="ts">
import { h } from "vue";
import { addDialog } from "@/components/ReDialog";
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

function openSupplierDialog(action: "新增" | "编辑", data?: Supplier) {
  addDialog({
    title: `${action}供应商`,
    width: "700px",
    draggable: true,
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: ({ options }) => h(SupplierForm, {
      initialData: data,
      manufacturers: manufacturerList.value,
      onSubmit: async (formData) => {
        try {
          if (action === "新增") {
            await createSupplier(formData);
          } else {
            await updateSupplier(data?.equipmentSupplierId, formData);
          }
          ElMessage.success(`${action}成功`);
          done(); // 关闭对话框
          onSearch(); // 刷新列表
        } catch (error) {
          ElMessage.error(`${action}失败`);
        }
      },
      onCancel: () => done()
    })
  });
}

// 使用示例
const handleAdd = () => openSupplierDialog("新增");
const handleEdit = (row: Supplier) => openSupplierDialog("编辑", row);
</script>
```

---

## 🎯 Page Header 组件

`page-header.vue` 是从React设备管理系统迁移而来的页面头部组件，用于显示页面标题、描述信息，提供返回和新增等操作功能。

### 功能特性：
- ✅ 显示页面标题和描述
- ✅ 可选的返回按钮（支持自定义回调）  
- ✅ 可选的新增按钮（支持自定义文字和回调）
- ✅ Flex布局，左右两端对齐
- ✅ 响应式布局适配
- ✅ 明暗主题支持

### 基本使用：
```vue
<script setup lang="ts">
import PageHeader from "@/views/equipment/components/page-header.vue";

const handleBack = () => router.go(-1);
const handleAdd = () => openDialog('新增');
</script>

<template>
  <PageHeader 
    title="设备管理"
    description="管理设备信息，包括新增、编辑、删除等操作"
    :show-back="true"
    :on-back="handleBack"
    :show-add="true"
    :on-add="handleAdd"
    add-label="新增设备"
  />
</template>
```

## 🔗 相关文件

### Status Badge
- `status-badge.vue` - 状态徽章主组件
- `usage-example.vue` - StatusBadge使用示例

### Page Header  
- `page-header.vue` - 页面头部主组件
- `page-header-example.vue` - PageHeader使用示例
- `page-header-migration-guide.md` - PageHeader详细迁移指南

### Data Table
- `data-table.vue` - 数据表格主组件
- `data-table-example.vue` - DataTable使用示例
- `data-table-migration-guide.md` - DataTable详细迁移指南

### Manufacturer Form
- `manufacturer-form.vue` - 制造商表单主组件
- `manufacturer-form-example.vue` - ManufacturerForm使用示例
- `manufacturer-form-migration-guide.md` - ManufacturerForm详细迁移指南

### Supplier Form
- `supplier-form.vue` - 供应商表单主组件
- `supplier-form-example.vue` - SupplierForm使用示例
- `supplier-form-migration-guide.md` - SupplierForm详细迁移指南

### 通用
- `README.md` - 本文档

## ❓ 常见问题

### Status Badge
**Q: 为什么使用el-tag而不是el-badge？**
A: el-tag更适合状态显示，与原React组件的视觉效果更接近。

**Q: 如何自定义颜色？**
A: 可以通过className传入自定义CSS类，或修改组件内的SCSS样式。

### Page Header
**Q: 如何自定义返回按钮的行为？**
A: 通过onBack回调函数实现，可以使用router.go(-1)或跳转到指定页面。

**Q: 新增按钮可以自定义样式吗？**
A: 可以通过CSS深度选择器修改，或者在父组件中重写样式。

### Data Table
**Q: 如何实现自定义渲染？**
A: 使用Vue3的h()函数，例如：`render: (value) => h(StatusBadge, { enabled: value })`

**Q: 如何处理大数据量？**
A: 建议结合分页或虚拟滚动，DataTable主要处理单页数据展示。

**Q: 能否自定义操作菜单？**
A: 当前版本提供固定的4种操作，如需更多自定义，可以扩展组件或使用插槽。

### Manufacturer Form
**Q: 如何自定义验证规则？**
A: 可以修改组件内的formRules对象，或通过ref获取表单实例进行自定义验证。

**Q: 如何处理表单提交失败？**
A: 在onSubmit回调中使用try-catch处理错误，并设置相应的isLoading状态。

**Q: 能否添加更多表单字段？**
A: 可以扩展组件，添加新的字段和相应的验证规则。

**Q: 组件是否支持国际化？**
A: 当前版本使用中文文案，后续可扩展支持i18n。

### Supplier Form
**Q: 制造商列表为空时怎么办？**
A: 组件会显示友好的空状态提示，建议在外部检查并引导用户先添加制造商。

**Q: 如何处理制造商数据加载失败？**
A: 建议在调用组件前先处理异常，提供空数组作为fallback。

**Q: 能否自定义制造商过滤逻辑？**
A: 可以在传入manufacturers前进行预过滤，组件内部只过滤enabled状态。

**Q: 如何验证制造商是否存在？**
A: 组件会验证选择的制造商ID是否有效，建议在提交前再次验证数据完整性。

**Q: 支持制造商数据的实时更新吗？**
A: 支持，manufacturers是响应式的，更新后组件会自动重新渲染下拉选项。

**Q: 如何处理长地址输入？**
A: 组件已设置最大200字符限制，建议引导用户输入简洁的地址信息。