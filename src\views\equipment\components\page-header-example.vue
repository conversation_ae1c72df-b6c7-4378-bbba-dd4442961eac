<script setup lang="ts">
/**
 * PageHeader组件使用示例
 * 展示如何在项目中使用迁移后的页面头部组件
 */
import { ref } from "vue";
import PageHeader from "./page-header.vue";

defineOptions({
  name: "PageHeaderUsageExample"
});

// 演示数据
const showDemo = ref(true);

// 模拟回调函数
const handleBack = () => {
  console.log("返回按钮被点击");
  // 在实际项目中可能是：
  // router.go(-1) 或 router.push('/equipment')
};

const handleAdd = () => {
  console.log("新增按钮被点击");
  // 在实际项目中可能是：
  // openDialog('新增')
};

const handleAddManufacturer = () => {
  console.log("新增制造商按钮被点击");
};
</script>

<template>
  <div class="page-header-usage-example">
    <el-card header="PageHeader组件使用示例">
      
      <!-- 基本使用 - 仅标题 -->
      <el-divider>基本使用 - 仅标题</el-divider>
      <div class="example-section">
        <PageHeader title="设备管理" />
      </div>

      <!-- 带描述信息 -->
      <el-divider>带描述信息</el-divider>
      <div class="example-section">
        <PageHeader 
          title="制造商管理" 
          description="管理设备制造商信息，包括新增、编辑、删除等操作"
        />
      </div>

      <!-- 带返回按钮 -->
      <el-divider>带返回按钮</el-divider>
      <div class="example-section">
        <PageHeader 
          title="制造商详情" 
          description="查看制造商的详细信息"
          :show-back="true"
          :on-back="handleBack"
        />
      </div>

      <!-- 带新增按钮 -->
      <el-divider>带新增按钮</el-divider>
      <div class="example-section">
        <PageHeader 
          title="供应商管理" 
          description="管理设备供应商信息"
          :show-add="true"
          :on-add="handleAdd"
        />
      </div>

      <!-- 自定义新增按钮文字 -->
      <el-divider>自定义新增按钮文字</el-divider>
      <div class="example-section">
        <PageHeader 
          title="制造商管理" 
          :show-add="true"
          :on-add="handleAddManufacturer"
          add-label="新增制造商"
        />
      </div>

      <!-- 完整功能 -->
      <el-divider>完整功能</el-divider>
      <div class="example-section">
        <PageHeader 
          title="联系人管理" 
          description="管理制造商和供应商的联系人信息"
          :show-back="true"
          :on-back="handleBack"
          :show-add="true"
          :on-add="handleAdd"
          add-label="新增联系人"
        />
      </div>

      <!-- 在实际页面中的使用示例 -->
      <el-divider>实际页面使用示例</el-divider>
      <div class="example-section">
        <!-- 模拟完整页面结构 -->
        <div class="mock-page">
          <PageHeader 
            title="设备类型管理" 
            description="管理设备分类信息，支持树形结构"
            :show-add="true"
            :on-add="handleAdd"
            add-label="新增设备类型"
          />
          
          <!-- 模拟页面内容 -->
          <div class="mock-content">
            <el-alert 
              title="这里是页面主要内容区域" 
              type="info" 
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>

      <!-- 功能对比说明 -->
      <el-divider>功能对比</el-divider>
      <div class="comparison-section">
        <el-descriptions title="React vs Vue3 功能对比" :column="1" border>
          <el-descriptions-item label="组件名称">
            <el-tag type="info">React: PageHeader</el-tag>
            <el-tag type="success">Vue3: EquipmentPageHeader</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Props">
            <div>
              <div>• title: string (必填)</div>
              <div>• description?: string (可选)</div>
              <div>• showBack?: boolean (可选，默认false)</div>
              <div>• onBack?: () => void (可选)</div>
              <div>• showAdd?: boolean (可选，默认false)</div>
              <div>• onAdd?: () => void (可选)</div>
              <div>• addLabel?: string (可选，默认"新增")</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="布局">
            <div>✅ Flex布局，左右两端对齐</div>
          </el-descriptions-item>
          <el-descriptions-item label="图标">
            <div>
              <el-tag type="info">React: Lucide ArrowLeft + Plus</el-tag>
              <el-tag type="success">Vue3: Element Plus Icons</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="响应式">
            <div>✅ 移动端自动调整为垂直布局</div>
          </el-descriptions-item>
          <el-descriptions-item label="主题支持">
            <div>✅ 支持明暗主题切换</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 使用建议 -->
      <el-divider>使用建议</el-divider>
      <div class="suggestions-section">
        <el-alert
          title="使用建议"
          type="success"
          :closable="false"
        >
          <template #default>
            <div class="suggestions-list">
              <p><strong>1. 标题层级：</strong>页面头部标题建议使用一级标题，避免嵌套使用</p>
              <p><strong>2. 描述文案：</strong>描述信息简洁明了，一般不超过30个字符</p>
              <p><strong>3. 按钮使用：</strong>返回按钮用于子页面，新增按钮用于列表页面</p>
              <p><strong>4. 响应式：</strong>组件在移动端会自动调整布局</p>
              <p><strong>5. 一致性：</strong>在同一模块中保持按钮文案和交互的一致性</p>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.page-header-usage-example {
  padding: 20px;
}

.example-section {
  margin: 16px 0;
  padding: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-bg-color-page);
}

.comparison-section {
  margin-top: 16px;
}

.suggestions-section {
  margin-top: 16px;
  
  .suggestions-list {
    p {
      margin: 8px 0;
      line-height: 1.6;
    }
  }
}

.mock-page {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 16px;
  background-color: var(--el-bg-color);
  
  .mock-content {
    margin-top: 24px;
    padding: 16px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
  }
}

// 暗色主题适配
.dark {
  .example-section {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color);
  }
  
  .mock-page {
    background-color: var(--el-bg-color);
    border-color: var(--el-border-color);
    
    .mock-content {
      background-color: var(--el-fill-color-darker);
    }
  }
}
</style>