import editForm from "../form/form.vue";
import { message, confirm } from "@/utils/message";
import {
  equipmentManufacturerCmdController,
  equipmentManufacturerQueryController,
  equipmentManufacturerContactCmdController,
  equipmentManufacturerContactQueryController
} from "@/api/apiInstance";
import { addDialog } from "@/components/ReDialog";
import { reactive, ref, onMounted, h } from "vue";
import type {
  CreateEquipmentManufacturerRequest,
  UpdateEquipmentManufacturerRequest,
  CreateEquipmentManufacturerContactRequest,
  UpdateEquipmentManufacturerContactRequest
} from "@/api/model/static";
import { cloneDeep, isAllEmpty, deviceDetection } from "@pureadmin/utils";
import type { Dynamic_EquipmentManufacturerRM } from "@/api/model/dynamic/Dynamic_EquipmentManufacturerRM";
import type { Dynamic_EquipmentManufacturerContactRM } from "@/api/model/dynamic/Dynamic_EquipmentManufacturerContactRM";
import type { FormProps, ContactFormProps } from "./types";

/**
 * 制造商管理 Composable 函数
 * 提供制造商的增删改查功能
 */
export function useManufacturer() {
  // 搜索表单数据
  const form = reactive({
    manufacturerName: "" // 制造商名称搜索字段
  });

  // 表单引用
  const formRef = ref();
  // 表格数据列表
  const dataList = ref([]);
  // 加载状态
  const loading = ref(true);

  // 表格列配置
  const columns: TableColumnList = [
    {
      label: "勾选列", // 如果需要表格多选，此处label必须设置
      type: "selection",
      fixed: "left",
      reserveSelection: true // 数据刷新后保留选项
    },
    {
      label: "制造商名称",
      prop: "manufacturerName",
      width: 200,
      align: "left"
    },
    {
      label: "备注",
      prop: "remarks",
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      label: "联系人数量",
      prop: "contactCount",
      width: 120,
      cellRenderer: ({ row }) => (
        <el-tag type="info">
          {row.contacts?.length ?? 0} 个
        </el-tag>
      )
    },
    {
      label: "状态",
      prop: "enabled",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <el-tag type={row.enabled ? "success" : "danger"}>
          {row.enabled ? "启用" : "禁用"}
        </el-tag>
      )
    },
    {
      label: "创建时间",
      minWidth: 200,
      prop: "createTime"
    },
    {
      label: "操作",
      fixed: "right",
      width: 280,
      slot: "operation"
    }
  ];

  /**
   * 重置搜索表单
   * @param formEl 表单元素引用
   * @param clearTableSelection 是否清除表格选择状态，默认为true
   */
  function resetForm(formEl: any, clearTableSelection: boolean = true) {
    if (!formEl) return;

    // 重置表单验证状态
    formEl.value?.resetFields();

    // 手动重置表单数据
    form.manufacturerName = "";

    // 根据参数决定是否清除表格选择状态
    if (clearTableSelection && typeof window !== "undefined") {
      // 触发自定义事件，通知表格组件清除选择
      window.dispatchEvent(new CustomEvent("clearTableSelection"));
    }

    // 重新搜索
    onSearch();
  }

  /**
   * 搜索制造商数据
   * 支持按制造商名称进行前端过滤
   */
  async function onSearch() {
    try {
      loading.value = true;
      // 调用API获取制造商数据
      const data = await equipmentManufacturerQueryController.getEquipmentManufacturer();
      let newData = data;

      // 如果输入了制造商名称，进行前端过滤
      if (!isAllEmpty(form.manufacturerName)) {
        // 前端搜索制造商名称
        newData = newData.filter(item =>
          item.manufacturerName?.includes(form.manufacturerName)
        );
      }

      dataList.value = newData;
    } finally {
      // 延时关闭加载状态，提升用户体验
      setTimeout(() => {
        loading.value = false;
      }, 500);
    }
  }

  /**
   * 打开编辑对话框
   * @param action 操作类型：'新增' 或 '修改'
   * @param row 当前行数据（修改时必传）
   */
  async function openDialog(
    action = "新增",
    row?: Dynamic_EquipmentManufacturerRM
  ) {
    /**
     * 获取表单初始数据
     * 根据不同操作类型返回相应的初始数据
     */
    const getFormData = () => {
      if (action === "修改") {
        // 修改时使用当前行数据
        return {
          equipmentManufacturerId: row?.equipmentManufacturerId ?? null,
          manufacturerName: row?.manufacturerName ?? "",
          remarks: row?.remarks ?? "",
          enabled: row?.enabled ?? true
        };
      }
      // 新增的默认数据
      return {
        equipmentManufacturerId: null,
        manufacturerName: "",
        remarks: "",
        enabled: true
      };
    };

    // 打开对话框
    addDialog({
      title: action === "修改" ? "修改制造商" : "新增制造商",
      props: {
        formInline: getFormData()
      },
      width: "40%",
      draggable: true,
      fullscreen: deviceDetection(), // 移动端自动全屏
      fullscreenIcon: true,
      closeOnClickModal: false,
      // 内容渲染器，使用h函数创建表单组件
      contentRenderer: ({ options }) =>
        h(editForm, {
          ref: formRef,
          formInline: options.props.formInline
        }),
      // 确认按钮点击前的回调
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormProps["formInline"];

        /**
         * 操作成功后的处理函数
         */
        function chores() {
          message(
            `您${action}了制造商名称为${curData.manufacturerName}的这条数据`,
            {
              type: "success"
            }
          );
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }

        // 表单验证
        FormRef.validate(async valid => {
          if (valid) {
            if (action === "新增") {
              // 构建新增制造商请求数据
              const req: CreateEquipmentManufacturerRequest = {
                manufacturerName: curData.manufacturerName,
                remarks: curData.remarks,
                enabled: curData.enabled ?? true
              };
              // 调用新增制造商API
              await equipmentManufacturerCmdController.createEquipmentManufacturer({
                body: req
              });
            } else if (action === "修改") {
              // 构建修改制造商请求数据
              const req: UpdateEquipmentManufacturerRequest = {
                equipmentManufacturerId: curData.equipmentManufacturerId,
                manufacturerName: curData.manufacturerName,
                remarks: curData.remarks,
                enabled: curData.enabled ?? true
              };
              // 调用修改制造商API
              await equipmentManufacturerCmdController.updateEquipmentManufacturer({
                body: req
              });
            }
            chores(); // 执行操作成功后的处理
          }
        });
      }
    });
  }

  /**
   * 删除制造商
   * @param row 要删除的制造商数据
   */
  async function handleDelete(row: Dynamic_EquipmentManufacturerRM) {
    // 调用删除制造商API
    await equipmentManufacturerCmdController.deleteEquipmentManufacturer({
      equipmentManufacturerId: row.equipmentManufacturerId
    });
    message(`您删除了制造商名称为${row.manufacturerName}的这条数据`, {
      type: "success"
    });
    onSearch(); // 刷新表格数据
  }

  /**
   * 批量删除制造商
   * @param selection 选中的制造商数据数组
   */
  async function onbatchDel(selection: Dynamic_EquipmentManufacturerRM[]) {
    if (selection.length === 0) {
      message("请先选择要删除的制造商", { type: "warning" });
      return;
    }

    // 确认批量删除
    try {
      await confirm(
        `确认删除选中的 ${selection.length} 个制造商吗？`,
        "批量删除确认"
      );

      // 逐个删除制造商
      for (const manufacturer of selection) {
        await equipmentManufacturerCmdController.deleteEquipmentManufacturer({
          equipmentManufacturerId: manufacturer.equipmentManufacturerId
        });
      }

      message(`成功删除 ${selection.length} 个制造商`, { type: "success" });
      onSearch(); // 刷新数据
    } catch {
      // 用户取消操作
    }
  }

  /**
   * 打开联系人管理对话框
   * @param row 制造商数据
   */
  function openContactDialog(row: Dynamic_EquipmentManufacturerRM) {
    const contactFormRef = ref();
    const contactList = ref<Dynamic_EquipmentManufacturerContactRM[]>([]);
    const contactLoading = ref(false);

    // 加载联系人数据
    const loadContacts = async () => {
      try {
        contactLoading.value = true;
        const data = await equipmentManufacturerContactQueryController.getEquipmentManufacturerContact({
          equipmentManufacturerId: row.equipmentManufacturerId
        });
        contactList.value = data;
      } finally {
        contactLoading.value = false;
      }
    };

    // 新增/编辑联系人
    const handleContactEdit = (contact?: Dynamic_EquipmentManufacturerContactRM) => {
      const getContactFormData = () => {
        if (contact) {
          return {
            equipmentManufacturerContactId: contact.equipmentManufacturerContactId,
            equipmentManufacturerId: row.equipmentManufacturerId,
            contactName: contact.contactName ?? "",
            contactPhone: contact.contactPhone ?? "",
            contactEmail: contact.contactEmail ?? "",
            contactRole: contact.contactRole ?? ""
          };
        }
        return {
          equipmentManufacturerContactId: null,
          equipmentManufacturerId: row.equipmentManufacturerId,
          contactName: "",
          contactPhone: "",
          contactEmail: "",
          contactRole: ""
        };
      };

      addDialog({
        title: contact ? "修改联系人" : "新增联系人",
        props: {
          formInline: getContactFormData()
        },
        width: "30%",
        draggable: true,
        closeOnClickModal: false,
        contentRenderer: ({ options }) =>
          h("div", [
            h("el-form", {
              ref: contactFormRef,
              model: options.props.formInline,
              labelWidth: "80px"
            }, [
              h("el-form-item", { label: "姓名", prop: "contactName" }, [
                h("el-input", {
                  modelValue: options.props.formInline.contactName,
                  "onUpdate:modelValue": (val) => options.props.formInline.contactName = val,
                  placeholder: "请输入联系人姓名"
                })
              ]),
              h("el-form-item", { label: "电话", prop: "contactPhone" }, [
                h("el-input", {
                  modelValue: options.props.formInline.contactPhone,
                  "onUpdate:modelValue": (val) => options.props.formInline.contactPhone = val,
                  placeholder: "请输入联系电话"
                })
              ]),
              h("el-form-item", { label: "邮箱", prop: "contactEmail" }, [
                h("el-input", {
                  modelValue: options.props.formInline.contactEmail,
                  "onUpdate:modelValue": (val) => options.props.formInline.contactEmail = val,
                  placeholder: "请输入邮箱地址"
                })
              ]),
              h("el-form-item", { label: "职位", prop: "contactRole" }, [
                h("el-input", {
                  modelValue: options.props.formInline.contactRole,
                  "onUpdate:modelValue": (val) => options.props.formInline.contactRole = val,
                  placeholder: "请输入职位"
                })
              ])
            ])
          ]),
        beforeSure: async (done, { options }) => {
          const contactData = options.props.formInline as ContactFormProps["formInline"];
          
          if (!contactData.contactName) {
            message("请输入联系人姓名", { type: "warning" });
            return;
          }

          try {
            if (contact) {
              // 修改联系人
              const req: UpdateEquipmentManufacturerContactRequest = {
                equipmentManufacturerContactId: contactData.equipmentManufacturerContactId,
                equipmentManufacturerId: contactData.equipmentManufacturerId,
                contactName: contactData.contactName,
                contactPhone: contactData.contactPhone,
                contactEmail: contactData.contactEmail,
                contactRole: contactData.contactRole
              };
              await equipmentManufacturerContactCmdController.updateEquipmentManufacturerContact({
                body: req
              });
            } else {
              // 新增联系人
              const req: CreateEquipmentManufacturerContactRequest = {
                equipmentManufacturerId: contactData.equipmentManufacturerId,
                contactName: contactData.contactName,
                contactPhone: contactData.contactPhone,
                contactEmail: contactData.contactEmail,
                contactRole: contactData.contactRole
              };
              await equipmentManufacturerContactCmdController.createEquipmentManufacturerContact({
                body: req
              });
            }
            
            message(`${contact ? "修改" : "新增"}联系人成功`, { type: "success" });
            done();
            loadContacts(); // 刷新联系人列表
          } catch (error) {
            console.error("操作联系人失败:", error);
          }
        }
      });
    };

    // 删除联系人
    const handleContactDelete = async (contact: Dynamic_EquipmentManufacturerContactRM) => {
      try {
        await confirm(`确认删除联系人「${contact.contactName}」？`, "删除确认");
        await equipmentManufacturerContactCmdController.deleteEquipmentManufacturerContact({
          equipmentManufacturerContactId: contact.equipmentManufacturerContactId
        });
        message("删除联系人成功", { type: "success" });
        loadContacts(); // 刷新联系人列表
      } catch {
        // 用户取消操作
      }
    };

    // 打开联系人管理对话框
    addDialog({
      title: `管理「${row.manufacturerName}」的联系人`,
      width: "60%",
      draggable: true,
      fullscreen: deviceDetection(),
      closeOnClickModal: false,
      contentRenderer: () =>
        h("div", { class: "contact-management" }, [
          h("div", { class: "contact-header mb-4" }, [
            h("el-button", {
              type: "primary",
              onClick: () => handleContactEdit()
            }, "新增联系人")
          ]),
          h("el-table", {
            data: contactList.value,
            loading: contactLoading.value,
            stripe: true
          }, [
            h("el-table-column", { prop: "contactName", label: "姓名", width: 120 }),
            h("el-table-column", { prop: "contactPhone", label: "电话", width: 140 }),
            h("el-table-column", { prop: "contactEmail", label: "邮箱", minWidth: 180 }),
            h("el-table-column", { prop: "contactRole", label: "职位", width: 120 }),
            h("el-table-column", {
              label: "操作",
              width: 150,
              default: ({ row: contact }) => [
                h("el-button", {
                  link: true,
                  type: "primary",
                  size: "small",
                  onClick: () => handleContactEdit(contact)
                }, "修改"),
                h("el-button", {
                  link: true,
                  type: "danger",
                  size: "small",
                  onClick: () => handleContactDelete(contact)
                }, "删除")
              ]
            })
          ])
        ]),
      open: () => {
        loadContacts(); // 打开时加载联系人数据
      }
    });
  }

  // 组件挂载时自动加载数据
  onMounted(() => {
    onSearch();
  });

  // 返回组合式函数的公共接口
  return {
    form, // 搜索表单数据
    loading, // 加载状态
    columns, // 表格列配置
    dataList, // 表格数据列表
    onSearch, // 搜索
    resetForm, // 重置
    openDialog, // 新增、修改制造商
    handleDelete, // 删除制造商
    onbatchDel, // 批量删除制造商
    openContactDialog // 管理联系人
  };
}