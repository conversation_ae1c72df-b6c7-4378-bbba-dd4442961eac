# SupplierForm 组件迁移指南

## 📋 组件概述

`supplier-form.vue` 是从React设备管理系统迁移而来的供应商表单组件，用于供应商信息的录入和编辑功能，包含制造商关联选择功能。

## 🎯 功能特性对比

### 原React组件特性
- ✅ 供应商名称输入（必填，1-100字符验证）
- ✅ 供应商地址输入（可选，最多200字符）
- ✅ 关联制造商选择（必填，下拉选择）
- ✅ 备注信息输入（可选，最多500字符）
- ✅ 启用状态切换（默认启用）
- ✅ 制造商过滤（只显示已启用的制造商）
- ✅ 表单验证（Zod schema）
- ✅ 加载状态支持
- ✅ 新增/编辑模式支持
- ✅ 基于React Hook Form + shadcn/ui

### Vue3迁移后特性
- ✅ 完全对等的表单字段和验证
- ✅ Element Plus表单组件
- ✅ Vue3 reactive响应式数据管理
- ✅ Element Plus验证规则
- ✅ computed自动过滤制造商
- ✅ 保持一致的用户交互
- ✅ 基于Vue3 Composition API + Element Plus
- 🎉 **增强功能**：制造商空状态提示
- 🎉 **增强功能**：下拉搜索过滤功能
- 🎉 **增强功能**：响应式布局适配
- 🎉 **增强功能**：明暗主题支持
- 🎉 **增强功能**：表单方法暴露

## 📦 API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| initialData | `Supplier` | `undefined` | 初始数据（编辑模式时传入） |
| manufacturers | `Manufacturer[]` | `[]` | 制造商列表（必填） |
| onSubmit | `(data: SupplierFormData) => void` | - | 表单提交回调（必填） |
| onCancel | `() => void` | - | 取消操作回调（必填） |
| isLoading | `boolean` | `false` | 是否处于加载状态 |

### Supplier 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| equipmentSupplierId | `number?` | 供应商ID |
| supplierName | `string?` | 供应商名称 |
| supplierAddress | `string?` | 供应商地址 |
| remarks | `string?` | 备注信息 |
| enabled | `boolean?` | 启用状态 |
| equipmentManufacturerId | `number?` | 关联制造商ID |
| manufacturerName | `string?` | 制造商名称 |
| createTime | `string?` | 创建时间 |
| creator | `string?` | 创建者 |
| editTime | `string?` | 编辑时间 |
| editor | `string?` | 编辑者 |

### Manufacturer 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| equipmentManufacturerId | `number?` | 制造商ID |
| manufacturerName | `string?` | 制造商名称 |
| remarks | `string?` | 备注信息 |
| enabled | `boolean?` | 启用状态 |
| createTime | `string?` | 创建时间 |
| creator | `string?` | 创建者 |
| editTime | `string?` | 编辑时间 |
| editor | `string?` | 编辑者 |

### SupplierFormData 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| supplierName | `string` | 供应商名称（必填） |
| supplierAddress | `string?` | 供应商地址（可选） |
| remarks | `string?` | 备注信息（可选） |
| enabled | `boolean?` | 启用状态（可选） |
| equipmentManufacturerId | `number` | 关联制造商ID（必填） |

### 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getRef | - | `FormInstance` | 获取表单实例引用 |
| resetForm | - | `void` | 重置表单到初始状态 |
| handleSubmit | - | `void` | 手动触发表单提交 |

## 📦 使用方法

### 基本使用 - 新增模式

```vue
<script setup lang="ts">
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

// 制造商列表（通常从API获取）
const manufacturerList = ref<Manufacturer[]>([
  { 
    equipmentManufacturerId: 1, 
    manufacturerName: "华为技术有限公司", 
    enabled: true 
  },
  { 
    equipmentManufacturerId: 2, 
    manufacturerName: "小米科技有限公司", 
    enabled: true 
  }
]);

const handleSubmit = (data: SupplierFormData) => {
  console.log("提交数据:", data);
  // 调用API创建供应商
  createSupplier(data);
};

const handleCancel = () => {
  console.log("取消操作");
  // 关闭对话框或返回列表页
};
</script>

<template>
  <SupplierForm
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>
```

### 编辑模式使用

```vue
<script setup lang="ts">
import { ref } from "vue";
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

// 编辑数据
const supplierData = ref({
  equipmentSupplierId: 1,
  supplierName: "深圳市创新供应链有限公司",
  supplierAddress: "深圳市南山区科技园",
  equipmentManufacturerId: 1,
  remarks: "专业的供应链服务商",
  enabled: true,
  createTime: "2023-01-20 11:00:00"
});

// 制造商列表
const manufacturerList = ref<Manufacturer[]>([...]);
const isLoading = ref(false);

const handleSubmit = async (data: SupplierFormData) => {
  try {
    isLoading.value = true;
    await updateSupplier(supplierData.value.equipmentSupplierId, data);
    ElMessage.success("更新成功");
  } catch (error) {
    ElMessage.error("更新失败");
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <SupplierForm
    :initial-data="supplierData"
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

### 制造商数据管理

```vue
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";

const manufacturerList = ref<Manufacturer[]>([]);

// 加载制造商列表
const loadManufacturers = async () => {
  try {
    const data = await equipmentManufacturerQueryController.getManufacturers();
    // 组件会自动过滤启用状态，这里无需过滤
    manufacturerList.value = data;
  } catch (error) {
    ElMessage.error("加载制造商列表失败");
    manufacturerList.value = [];
  }
};

// 监听制造商变化
watch(manufacturerList, (newList) => {
  const enabledCount = newList.filter(m => m.enabled).length;
  if (enabledCount === 0) {
    ElMessage.warning("暂无可用制造商，请先添加制造商");
  }
}, { immediate: true });

onMounted(() => {
  loadManufacturers();
});
</script>

<template>
  <SupplierForm
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>
```

### 在对话框中使用

```vue
<script setup lang="ts">
import { h } from "vue";
import { addDialog } from "@/components/ReDialog";
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

function openSupplierDialog(action: "新增" | "编辑", data?: Supplier) {
  addDialog({
    title: `${action}供应商`,
    width: "700px",
    draggable: true,
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: ({ options }) => h(SupplierForm, {
      initialData: data,
      manufacturers: manufacturerList.value,
      onSubmit: async (formData) => {
        try {
          if (action === "新增") {
            await createSupplier(formData);
          } else {
            await updateSupplier(data?.equipmentSupplierId, formData);
          }
          ElMessage.success(`${action}成功`);
          done(); // 关闭对话框
          onSearch(); // 刷新列表
        } catch (error) {
          ElMessage.error(`${action}失败`);
        }
      },
      onCancel: () => done()
    })
  });
}

// 使用示例
const handleAdd = () => openSupplierDialog("新增");
const handleEdit = (row: Supplier) => openSupplierDialog("编辑", row);
</script>
```

## 🔄 迁移现有代码

### 1. 替换React表单组件

**原React代码:**
```tsx
import { SupplierForm } from "@/components/supplier-form";

const formSchema = z.object({
  supplier_name: z.string().min(1).max(100),
  supplier_address: z.string().max(200).optional(),
  equipment_manufacturer_id: z.number().min(1),
  remarks: z.string().max(500).optional(),
  enabled: z.boolean().default(true),
});

<SupplierForm 
  initialData={supplierData}
  manufacturers={manufacturerList}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  isLoading={isLoading}
/>
```

**Vue3代码:**
```vue
<script setup lang="ts">
import SupplierForm from "@/views/equipment/components/supplier-form.vue";

// 验证规则已内置在组件中，无需外部定义
const handleSubmit = (data: SupplierFormData) => {
  // 处理提交逻辑
};
</script>

<template>
  <SupplierForm 
    :initial-data="supplierData"
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

### 2. 验证规则迁移

**React (Zod schema):**
```typescript
const formSchema = z.object({
  supplier_name: z.string().min(1, "供应商名称不能为空").max(100, "供应商名称不能超过100个字符"),
  supplier_address: z.string().max(200, "地址不能超过200个字符").optional(),
  equipment_manufacturer_id: z.number().min(1, "请选择关联制造商"),
  remarks: z.string().max(500, "备注不能超过500个字符").optional(),
  enabled: z.boolean().default(true),
});
```

**Vue3 (Element Plus rules):**
```typescript
// 已内置在组件中
const formRules = {
  supplierName: [
    { required: true, message: "供应商名称不能为空", trigger: "blur" },
    { max: 100, message: "供应商名称不能超过100个字符", trigger: "blur" }
  ],
  supplierAddress: [
    { max: 200, message: "地址不能超过200个字符", trigger: "blur" }
  ],
  equipmentManufacturerId: [
    { type: "number", min: 1, message: "请选择关联制造商", trigger: "change" }
  ],
  remarks: [
    { max: 500, message: "备注不能超过500个字符", trigger: "blur" }
  ]
};
```

### 3. 制造商过滤逻辑迁移

**React 过滤:**
```typescript
{manufacturers
  .filter((m) => m.enabled)
  .map((manufacturer) => (
    <SelectItem
      key={manufacturer.equipment_manufacturer_id}
      value={String(manufacturer.equipment_manufacturer_id)}
    >
      {manufacturer.manufacturer_name}
    </SelectItem>
  ))
}
```

**Vue3 computed 过滤:**
```typescript
// 组件内部使用computed自动过滤
const enabledManufacturers = computed(() => {
  return props.manufacturers
    .filter(m => m.enabled)
    .map(m => ({
      value: m.equipmentManufacturerId!,
      label: m.manufacturerName!
    }));
});
```

## 🎨 样式定制

### 自定义表单样式

```vue
<template>
  <SupplierForm 
    class="custom-supplier-form"
    :manufacturers="manufacturerList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>

<style scoped>
.custom-supplier-form {
  :deep(.el-form-item__label) {
    color: var(--el-color-primary);
    font-weight: 600;
  }
  
  :deep(.manufacturer-empty-tip) {
    background-color: var(--el-color-warning-light-9);
    padding: 8px;
    border-radius: 4px;
  }
}
</style>
```

### 自定义制造商选择器

```vue
<style scoped>
.custom-supplier-form {
  :deep(.el-select) {
    .el-input__wrapper {
      border-color: var(--el-color-primary);
    }
    
    .el-select__placeholder {
      color: var(--el-text-color-placeholder);
    }
  }
}
</style>
```

## 🔧 技术实现细节

### 制造商过滤

Vue3使用computed自动过滤已启用的制造商：

```typescript
const enabledManufacturers = computed(() => {
  return props.manufacturers
    .filter(m => m.enabled)
    .map(m => ({
      value: m.equipmentManufacturerId!,
      label: m.manufacturerName!
    }));
});
```

### 表单数据管理

使用reactive管理表单状态，支持双向绑定：

```typescript
const formData = reactive<SupplierFormData>({
  supplierName: props.initialData?.supplierName || "",
  supplierAddress: props.initialData?.supplierAddress || "",
  equipmentManufacturerId: props.initialData?.equipmentManufacturerId || 0,
  // ...其他字段
});
```

### 数值类型处理

Element Plus Select自动处理数值类型转换：

```vue
<el-select v-model="formData.equipmentManufacturerId">
  <el-option 
    :value="manufacturer.value"  <!-- 自动转换为number -->
    :label="manufacturer.label"
  />
</el-select>
```

## 🧪 测试用例

```vue
<script setup lang="ts">
// 测试数据
const testCases = [
  {
    name: "新增模式",
    data: undefined,
    manufacturers: mockManufacturers,
    expected: "空表单，制造商下拉可选"
  },
  {
    name: "编辑模式 - 完整数据",
    data: {
      supplierName: "深圳创新供应链",
      supplierAddress: "深圳市南山区",
      equipmentManufacturerId: 1,
      remarks: "专业服务商",
      enabled: true
    },
    manufacturers: mockManufacturers,
    expected: "表单填充完整数据"
  },
  {
    name: "制造商为空",
    data: undefined,
    manufacturers: [],
    expected: "显示制造商空状态提示"
  },
  {
    name: "只有禁用制造商",
    data: undefined,
    manufacturers: [{ id: 1, name: "禁用制造商", enabled: false }],
    expected: "下拉为空，显示空状态提示"
  }
];

// 验证测试
const runValidationTests = () => {
  // 测试必填验证
  // 测试长度限制验证
  // 测试制造商选择验证
  // 测试地址格式验证
};
</script>
```

## 🚀 最佳实践

1. **制造商数据**: 确保传入有效的制造商列表，至少包含一个启用的制造商
2. **数据初始化**: 编辑模式时确保传入完整且正确的initialData
3. **错误处理**: 在onSubmit回调中添加完善的错误处理逻辑
4. **加载状态**: 在异步操作期间正确设置isLoading状态
5. **数据验证**: 利用组件内置验证规则，也可以通过ref进行自定义验证
6. **制造商管理**: 定期检查制造商列表的有效性，提供友好的空状态提示
7. **用户体验**: 适当使用ElMessage提供操作反馈
8. **响应式**: 组件已内置响应式支持，无需额外处理

## 📋 迁移检查清单

- [ ] 导入SupplierForm组件
- [ ] 定义SupplierFormData接口
- [ ] 准备制造商数据列表
- [ ] 实现onSubmit回调函数
- [ ] 实现onCancel回调函数
- [ ] 测试新增模式功能
- [ ] 测试编辑模式功能
- [ ] 验证制造商关联功能
- [ ] 测试制造商过滤逻辑
- [ ] 验证表单验证规则
- [ ] 测试加载状态
- [ ] 检查制造商空状态处理
- [ ] 验证响应式布局
- [ ] 测试明暗主题切换
- [ ] 更新相关文档

## 🔗 相关文件

- `supplier-form.vue` - 主组件文件
- `supplier-form-example.vue` - 使用示例
- `supplier-form-migration-guide.md` - 本迁移指南

## ❓ 常见问题

**Q: 制造商列表为空时怎么办？**
A: 组件会显示友好的空状态提示，建议在外部检查并引导用户先添加制造商。

**Q: 如何处理制造商数据加载失败？**
A: 建议在调用组件前先处理异常，提供空数组作为fallback。

**Q: 能否自定义制造商过滤逻辑？**
A: 可以在传入manufacturers前进行预过滤，组件内部只过滤enabled状态。

**Q: 如何验证制造商是否存在？**
A: 组件会验证选择的制造商ID是否有效，建议在提交前再次验证数据完整性。

**Q: 支持制造商数据的实时更新吗？**
A: 支持，manufacturers是响应式的，更新后组件会自动重新渲染下拉选项。

**Q: 如何处理长地址输入？**
A: 组件已设置最大200字符限制，建议引导用户输入简洁的地址信息。