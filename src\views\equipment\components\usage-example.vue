<script setup lang="ts">
/**
 * StatusBadge组件使用示例
 * 展示如何在项目中使用迁移后的状态徽章组件
 */
import { ref } from "vue";
import StatusBadge from "./status-badge.vue";

defineOptions({
  name: "StatusBadgeUsageExample"
});

// 模拟数据
const mockData = ref([
  { id: 1, name: "制造商A", enabled: true },
  { id: 2, name: "制造商B", enabled: false },
  { id: 3, name: "供应商A", enabled: true },
  { id: 4, name: "供应商B", enabled: false }
]);

// 表格列配置 - 展示如何在表格中使用StatusBadge组件
const columns = [
  {
    label: "名称",
    prop: "name",
    width: 200
  },
  {
    label: "状态",
    prop: "enabled",
    width: 100,
    // 使用cellRenderer渲染StatusBadge组件
    cellRenderer: ({ row }: { row: any }) => (
      <StatusBadge enabled={row.enabled} />
    )
  }
];
</script>

<template>
  <div class="status-badge-usage-example">
    <el-card header="StatusBadge组件使用示例">
      <!-- 基本使用 -->
      <el-divider>基本使用</el-divider>
      <div class="example-section">
        <p>启用状态：</p>
        <StatusBadge :enabled="true" />
        
        <p style="margin-top: 16px;">禁用状态：</p>
        <StatusBadge :enabled="false" />
      </div>

      <!-- 自定义className -->
      <el-divider>自定义样式</el-divider>
      <div class="example-section">
        <p>带自定义CSS类：</p>
        <StatusBadge :enabled="true" class-name="custom-badge" />
        <StatusBadge :enabled="false" class-name="custom-badge" />
      </div>

      <!-- 在表格中使用 -->
      <el-divider>在表格中使用</el-divider>
      <div class="example-section">
        <el-table :data="mockData" border style="width: 100%">
          <el-table-column
            prop="name"
            label="名称"
            width="200"
          />
          <el-table-column
            prop="enabled"
            label="状态"
            width="100"
          >
            <template #default="{ row }">
              <StatusBadge :enabled="row.enabled" />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 功能对比说明 -->
      <el-divider>功能对比</el-divider>
      <div class="comparison-section">
        <el-descriptions title="React vs Vue3 功能对比" :column="2" border>
          <el-descriptions-item label="组件名称">
            <el-tag type="info">React: StatusBadge</el-tag>
            <el-tag type="success">Vue3: EquipmentStatusBadge</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Props">
            <div>enabled: boolean, className?: string</div>
          </el-descriptions-item>
          <el-descriptions-item label="显示逻辑">
            <div>完全一致：enabled ? "启用" : "禁用"</div>
          </el-descriptions-item>
          <el-descriptions-item label="样式">
            <div>颜色完全对应：绿色(启用) / 灰色(禁用)</div>
          </el-descriptions-item>
          <el-descriptions-item label="基础组件">
            <el-tag type="info">React: shadcn/ui Badge</el-tag>
            <el-tag type="success">Vue3: Element Plus Tag</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="类型安全">
            <div>✅ 完整的TypeScript支持</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.status-badge-usage-example {
  padding: 20px;
}

.example-section {
  margin: 16px 0;
  
  p {
    margin: 8px 0;
    font-weight: 500;
  }
}

.comparison-section {
  margin-top: 16px;
}

// 演示自定义样式的例子
:deep(.custom-badge) {
  margin-right: 8px;
  font-weight: bold;
}
</style>