<script setup lang="ts">
/**
 * ContactForm组件使用示例
 * 展示如何在项目中使用迁移后的联系人表单组件
 */
import { ref, reactive } from "vue";
import ContactForm from "./contact-form.vue";

defineOptions({
  name: "ContactFormUsageExample"
});

// 模拟制造商数据类型
interface Manufacturer {
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  remarks?: string;
  enabled?: boolean;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 模拟供应商数据类型
interface Supplier {
  equipmentSupplierId?: number;
  supplierName?: string;
  supplierAddress?: string;
  remarks?: string;
  enabled?: boolean;
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 模拟联系人数据类型
interface Contact {
  equipmentContactId?: number;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  entityType?: "manufacturer" | "supplier";
  entityId?: number;
  entityName?: string;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 模拟表单数据类型
interface ContactFormData {
  contactName: string;
  contactPhone?: string;
  contactEmail?: string;
  contactRole?: string;
  entityType: "manufacturer" | "supplier";
  entityId: number;
}

// 状态管理
const currentMode = ref<"create" | "edit">("create");
const isLoading = ref(false);
const showForm = ref(true);

// 模拟制造商数据
const manufacturerList = reactive<Manufacturer[]>([
  {
    equipmentManufacturerId: 1,
    manufacturerName: "华为技术有限公司",
    remarks: "全球领先的ICT基础设施和智能终端提供商",
    enabled: true,
    createTime: "2023-01-15 10:30:00",
    creator: "系统管理员"
  },
  {
    equipmentManufacturerId: 2,
    manufacturerName: "小米科技有限公司",
    remarks: "专注于智能硬件和电子产品研发",
    enabled: true,
    createTime: "2023-02-20 14:20:00",
    creator: "业务管理员"
  },
  {
    equipmentManufacturerId: 3,
    manufacturerName: "比亚迪股份有限公司",
    remarks: "新能源汽车及动力电池制造商",
    enabled: false, // 禁用状态，不会在下拉中显示
    createTime: "2023-03-10 09:15:00",
    creator: "业务管理员"
  },
  {
    equipmentManufacturerId: 4,
    manufacturerName: "腾讯科技有限公司",
    remarks: "互联网综合服务提供商",
    enabled: true,
    createTime: "2023-04-05 16:45:00",
    creator: "系统管理员"
  }
]);

// 模拟供应商数据
const supplierList = reactive<Supplier[]>([
  {
    equipmentSupplierId: 1,
    supplierName: "深圳市创新供应链有限公司",
    supplierAddress: "深圳市南山区科技园南区深南大道10000号",
    remarks: "专业的电子产品供应链服务商",
    enabled: true,
    equipmentManufacturerId: 1,
    manufacturerName: "华为技术有限公司",
    createTime: "2023-01-20 11:00:00",
    creator: "采购管理员"
  },
  {
    equipmentSupplierId: 2,
    supplierName: "上海智能物流有限公司",
    supplierAddress: "上海市浦东新区张江高科技园区科学城路100号",
    remarks: "专业从事智能物流解决方案的高新技术企业",
    enabled: true,
    equipmentManufacturerId: 2,
    manufacturerName: "小米科技有限公司",
    createTime: "2023-02-25 16:30:00",
    creator: "物流管理员"
  },
  {
    equipmentSupplierId: 3,
    supplierName: "北京科技服务有限公司",
    supplierAddress: "北京市海淀区中关村大街1号",
    remarks: "提供专业的技术服务和解决方案",
    enabled: false, // 禁用状态，不会在下拉中显示
    equipmentManufacturerId: 4,
    manufacturerName: "腾讯科技有限公司",
    createTime: "2023-03-15 14:20:00",
    creator: "技术管理员"
  }
]);

// 模拟编辑数据 - 制造商联系人
const editDataManufacturer = reactive<Contact>({
  equipmentContactId: 1,
  contactName: "张伟",
  contactPhone: "13800138001",
  contactEmail: "<EMAIL>",
  contactRole: "技术经理",
  entityType: "manufacturer",
  entityId: 1,
  entityName: "华为技术有限公司",
  createTime: "2023-01-20 11:00:00",
  creator: "系统管理员"
});

// 模拟编辑数据 - 供应商联系人
const editDataSupplier = reactive<Contact>({
  equipmentContactId: 2,
  contactName: "李娜",
  contactPhone: "13900139002",
  contactEmail: "<EMAIL>",
  contactRole: "销售总监",
  entityType: "supplier",
  entityId: 1,
  entityName: "深圳市创新供应链有限公司",
  createTime: "2023-02-10 15:30:00",
  creator: "业务管理员"
});

// 模拟表单提交
const handleSubmit = (data: ContactFormData) => {
  console.log("表单提交数据:", data);
  
  isLoading.value = true;
  
  // 模拟API调用
  setTimeout(() => {
    isLoading.value = false;
    
    const entityName = data.entityType === "manufacturer" 
      ? manufacturerList.find(m => m.equipmentManufacturerId === data.entityId)?.manufacturerName
      : supplierList.find(s => s.equipmentSupplierId === data.entityId)?.supplierName;
    
    if (currentMode.value === "create") {
      ElMessage.success(`成功创建联系人「${data.contactName}」，关联到${entityName}`);
    } else {
      ElMessage.success(`成功更新联系人「${data.contactName}」信息`);
    }
    
    // 模拟表单重置或关闭
    // showForm.value = false;
  }, 2000);
};

// 取消操作
const handleCancel = () => {
  console.log("取消操作");
  ElMessage.info("已取消操作");
  showForm.value = false;
};

// 切换模式
const switchMode = (mode: "create" | "edit") => {
  currentMode.value = mode;
  showForm.value = true;
  isLoading.value = false;
};

// 重新显示表单
const showFormAgain = () => {
  showForm.value = true;
};

// 切换制造商状态
const toggleManufacturerStatus = (id: number) => {
  const manufacturer = manufacturerList.find(m => m.equipmentManufacturerId === id);
  if (manufacturer) {
    manufacturer.enabled = !manufacturer.enabled;
    ElMessage.info(`${manufacturer.manufacturerName} 状态已${manufacturer.enabled ? '启用' : '禁用'}`);
  }
};

// 切换供应商状态
const toggleSupplierStatus = (id: number) => {
  const supplier = supplierList.find(s => s.equipmentSupplierId === id);
  if (supplier) {
    supplier.enabled = !supplier.enabled;
    ElMessage.info(`${supplier.supplierName} 状态已${supplier.enabled ? '启用' : '禁用'}`);
  }
};

// 添加制造商
const addManufacturer = () => {
  const newId = Math.max(...manufacturerList.map(m => m.equipmentManufacturerId!)) + 1;
  manufacturerList.push({
    equipmentManufacturerId: newId,
    manufacturerName: `新制造商${newId}`,
    remarks: "新添加的制造商",
    enabled: true,
    createTime: new Date().toLocaleString(),
    creator: "示例用户"
  });
  ElMessage.success("已添加新制造商");
};

// 添加供应商
const addSupplier = () => {
  const newId = Math.max(...supplierList.map(s => s.equipmentSupplierId!)) + 1;
  supplierList.push({
    equipmentSupplierId: newId,
    supplierName: `新供应商${newId}`,
    supplierAddress: "新地址",
    remarks: "新添加的供应商",
    enabled: true,
    equipmentManufacturerId: 1,
    manufacturerName: "华为技术有限公司",
    createTime: new Date().toLocaleString(),
    creator: "示例用户"
  });
  ElMessage.success("已添加新供应商");
};

// 获取当前编辑数据
const getCurrentEditData = () => {
  if (currentMode.value === "edit") {
    // 根据当前示例展示不同类型的编辑数据
    return Math.random() > 0.5 ? editDataManufacturer : editDataSupplier;
  }
  return undefined;
};
</script>

<template>
  <div class="contact-form-usage-example">
    <el-card header="ContactForm组件使用示例">
      
      <!-- 制造商管理控制 -->
      <el-divider>制造商数据管理</el-divider>
      <div class="entity-controls">
        <div class="entity-list">
          <el-tag
            v-for="manufacturer in manufacturerList"
            :key="manufacturer.equipmentManufacturerId"
            :type="manufacturer.enabled ? 'success' : 'danger'"
            closable
            @close="toggleManufacturerStatus(manufacturer.equipmentManufacturerId!)"
            style="margin: 4px;"
          >
            {{ manufacturer.manufacturerName }}
            {{ manufacturer.enabled ? '(启用)' : '(禁用)' }}
          </el-tag>
        </div>
        <el-button size="small" @click="addManufacturer">添加制造商</el-button>
      </div>

      <!-- 供应商管理控制 -->
      <el-divider>供应商数据管理</el-divider>
      <div class="entity-controls">
        <div class="entity-list">
          <el-tag
            v-for="supplier in supplierList"
            :key="supplier.equipmentSupplierId"
            :type="supplier.enabled ? 'primary' : 'info'"
            closable
            @close="toggleSupplierStatus(supplier.equipmentSupplierId!)"
            style="margin: 4px;"
          >
            {{ supplier.supplierName }}
            {{ supplier.enabled ? '(启用)' : '(禁用)' }}
          </el-tag>
        </div>
        <el-button size="small" @click="addSupplier">添加供应商</el-button>
      </div>

      <!-- 模式切换控制 -->
      <el-divider>模式切换</el-divider>
      <div class="mode-controls">
        <el-radio-group v-model="currentMode" @change="switchMode">
          <el-radio-button value="create">新增模式</el-radio-button>
          <el-radio-button value="edit">编辑模式</el-radio-button>
        </el-radio-group>
        
        <el-button 
          v-if="!showForm"
          type="primary"
          @click="showFormAgain"
        >
          重新显示表单
        </el-button>
      </div>

      <!-- 当前模式信息 -->
      <el-alert
        :title="`当前模式: ${currentMode === 'create' ? '新增联系人' : '编辑联系人'}`"
        :type="currentMode === 'create' ? 'success' : 'warning'"
        show-icon
        :closable="false"
        style="margin: 16px 0;"
      />

      <!-- 表单示例 -->
      <el-divider>表单示例</el-divider>
      <div v-if="showForm" class="form-container">
        <ContactForm
          :initial-data="getCurrentEditData()"
          :manufacturers="manufacturerList"
          :suppliers="supplierList"
          :on-submit="handleSubmit"
          :on-cancel="handleCancel"
          :is-loading="isLoading"
        />
      </div>
      
      <div v-else class="form-hidden">
        <el-result
          icon="success"
          title="表单已隐藏"
          sub-title="点击上方按钮重新显示表单"
        />
      </div>

      <!-- 不同数据状态示例 -->
      <el-divider>不同数据状态示例</el-divider>
      <div class="data-examples">
        <el-tabs>
          <el-tab-pane label="制造商联系人" name="manufacturer">
            <div class="example-data">
              <h4>制造商联系人数据：</h4>
              <pre><code>{{ JSON.stringify(editDataManufacturer, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="供应商联系人" name="supplier">
            <div class="example-data">
              <h4>供应商联系人数据：</h4>
              <pre><code>{{ JSON.stringify(editDataSupplier, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="制造商列表" name="manufacturers">
            <div class="example-data">
              <h4>可用制造商列表：</h4>
              <pre><code>{{ JSON.stringify(manufacturerList, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="供应商列表" name="suppliers">
            <div class="example-data">
              <h4>可用供应商列表：</h4>
              <pre><code>{{ JSON.stringify(supplierList, null, 2) }}</code></pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 功能对比说明 -->
      <el-divider>功能对比</el-divider>
      <div class="comparison-section">
        <el-descriptions title="React vs Vue3 功能对比" :column="1" border>
          <el-descriptions-item label="组件名称">
            <el-tag type="info">React: ContactForm</el-tag>
            <el-tag type="success">Vue3: EquipmentContactForm</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="表单字段">
            <div>
              <div>• contactName: 联系人姓名（必填，1-50字符）</div>
              <div>• contactPhone: 联系电话（可选，手机号码格式）</div>
              <div>• contactEmail: 邮箱地址（可选，邮箱格式）</div>
              <div>• contactRole: 职位（可选，最多50字符）</div>
              <div>• entityType: 关联类型（必填，制造商/供应商）</div>
              <div>• entityId: 关联实体ID（必填）</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="动态切换功能">
            <div>✅ 实体类型切换（制造商/供应商）</div>
            <div>✅ 动态实体选项（根据类型过滤）</div>
            <div>✅ 自动清空已选实体（类型变更时）</div>
          </el-descriptions-item>
          <el-descriptions-item label="验证规则">
            <div>
              <el-tag type="info">React: Zod Schema + 正则</el-tag>
              <el-tag type="success">Vue3: Element Plus Rules + 自定义验证器</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="表单管理">
            <div>
              <el-tag type="info">React: useForm + zodResolver + watch</el-tag>
              <el-tag type="success">Vue3: reactive + el-form + watch</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="UI组件">
            <div>
              <el-tag type="info">React: shadcn/ui</el-tag>
              <el-tag type="success">Vue3: Element Plus</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="增强功能">
            <div>
              <div>✅ 实体空状态提示</div>
              <div>✅ 响应式布局适配</div>
              <div>✅ 明暗主题支持</div>
              <div>✅ 表单方法暴露</div>
              <div>✅ 过滤和搜索功能</div>
              <div>✅ 自动验证清除</div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 最佳实践建议 -->
      <el-divider>最佳实践建议</el-divider>
      <div class="best-practices">
        <el-alert
          title="使用建议"
          type="info"
          :closable="false"
        >
          <template #default>
            <div class="practices-list">
              <p><strong>1. 数据准备：</strong>确保传入有效的制造商和供应商列表，至少包含一个启用的实体</p>
              <p><strong>2. 验证处理：</strong>手机号码和邮箱使用自定义验证器，支持空值但格式必须正确</p>
              <p><strong>3. 类型切换：</strong>实体类型变更时自动重置实体选择，避免无效数据</p>
              <p><strong>4. 错误处理：</strong>在onSubmit中添加适当的错误处理和用户反馈</p>
              <p><strong>5. 数据验证：</strong>利用内置验证规则，也可以通过ref进行自定义验证</p>
              <p><strong>6. 实体过滤：</strong>组件会自动过滤只显示启用的制造商和供应商</p>
              <p><strong>7. 响应式：</strong>组件已内置响应式支持，在移动端自动调整布局</p>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.contact-form-usage-example {
  padding: 20px;
}

.entity-controls {
  margin: 16px 0;
  
  .entity-list {
    margin-bottom: 12px;
    min-height: 40px;
    padding: 8px;
    border: 1px dashed var(--el-border-color);
    border-radius: 4px;
    background-color: var(--el-fill-color-lighter);
  }
}

.mode-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}

.form-container {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  background-color: var(--el-bg-color-page);
}

.form-hidden {
  text-align: center;
  padding: 40px 20px;
}

.data-examples {
  margin: 16px 0;
  
  .example-data {
    h4 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
    }
    
    pre {
      background-color: var(--el-fill-color-lighter);
      padding: 16px;
      border-radius: 4px;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

.comparison-section {
  margin-top: 16px;
}

.best-practices {
  margin-top: 16px;
  
  .practices-list {
    p {
      margin: 8px 0;
      line-height: 1.6;
    }
  }
}

// 暗色主题适配
.dark {
  .form-container {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color);
  }
  
  .entity-controls .entity-list {
    background-color: var(--el-fill-color-darker);
    border-color: var(--el-border-color);
  }
  
  .data-examples {
    pre {
      background-color: var(--el-fill-color-darker);
    }
  }
}
</style>