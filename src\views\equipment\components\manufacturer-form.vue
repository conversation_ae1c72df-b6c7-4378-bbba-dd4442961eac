<script setup lang="ts">
/**
 * 制造商表单组件
 * 从React设备管理系统迁移而来的制造商信息录入表单
 * 与原React组件功能完全对等，支持新增和编辑模式
 */
import { reactive, ref } from "vue";
import ReCol from "@/components/ReCol";

defineOptions({
  name: "EquipmentManufacturerForm"
});

// 制造商数据接口
interface Manufacturer {
  equipmentManufacturerId?: number;
  manufacturerName?: string;
  remarks?: string;
  enabled?: boolean;
  createTime?: string;
  creator?: string;
  editTime?: string;
  editor?: string;
}

// 表单数据接口
interface ManufacturerFormData {
  manufacturerName: string;
  remarks?: string;
  enabled?: boolean;
}

// 组件Props接口
interface ManufacturerFormProps {
  /** 初始数据（编辑模式时传入） */
  initialData?: Manufacturer;
  /** 表单提交回调 */
  onSubmit: (data: ManufacturerFormData) => void;
  /** 取消操作回调 */
  onCancel: () => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
}

const props = withDefaults(defineProps<ManufacturerFormProps>(), {
  initialData: undefined,
  isLoading: false
});

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive<ManufacturerFormData>({
  manufacturerName: props.initialData?.manufacturerName || "",
  remarks: props.initialData?.remarks || "",
  enabled: props.initialData?.enabled ?? true
});

// 表单验证规则
const formRules = {
  manufacturerName: [
    { required: true, message: "制造商名称不能为空", trigger: "blur" },
    { max: 100, message: "制造商名称不能超过100个字符", trigger: "blur" }
  ],
  remarks: [
    { max: 500, message: "备注不能超过500个字符", trigger: "blur" }
  ]
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      props.onSubmit({
        manufacturerName: formData.manufacturerName,
        remarks: formData.remarks,
        enabled: formData.enabled
      });
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  // 恢复初始数据
  formData.manufacturerName = props.initialData?.manufacturerName || "";
  formData.remarks = props.initialData?.remarks || "";
  formData.enabled = props.initialData?.enabled ?? true;
};

// 暴露方法给父组件
const getRef = () => {
  return formRef.value;
};

defineExpose({ 
  getRef,
  resetForm,
  handleSubmit
});
</script>

<template>
  <div class="manufacturer-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="top"
    >
      <!-- 制造商名称 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="制造商名称" prop="manufacturerName">
            <el-input
              v-model="formData.manufacturerName"
              placeholder="请输入制造商名称"
              clearable
              :disabled="isLoading"
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 备注信息 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              :disabled="isLoading"
              resize="none"
              clearable
            />
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 启用状态 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <el-form-item label="启用状态">
            <div class="status-switch-container">
              <div class="status-info">
                <div class="status-label">启用状态</div>
                <div class="status-description">启用后该制造商将可以被选择和使用</div>
              </div>
              <el-switch
                v-model="formData.enabled"
                :disabled="isLoading"
                inline-prompt
                active-text="启用"
                inactive-text="禁用"
              />
            </div>
          </el-form-item>
        </re-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-row :gutter="20">
        <re-col :value="24">
          <div class="form-actions">
            <el-button
              :disabled="isLoading"
              @click="onCancel"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              :loading="isLoading"
              @click="handleSubmit"
            >
              {{ isLoading ? "保存中..." : "保存" }}
            </el-button>
          </div>
        </re-col>
      </el-row>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.manufacturer-form {
  padding: 20px;
  
  // 状态开关容器样式 - 对应原组件的flex布局
  .status-switch-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    background-color: var(--el-bg-color-page);
    
    .status-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .status-label {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
      
      .status-description {
        font-size: 14px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
    }
  }
  
  // 表单操作按钮区域 - 对应原组件的右对齐布局
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 24px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .manufacturer-form {
    padding: 16px;
    
    .status-switch-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .status-info {
        width: 100%;
      }
    }
    
    .form-actions {
      flex-direction: column-reverse;
      
      .el-button {
        width: 100%;
      }
    }
  }
}

// 暗色主题适配
.dark {
  .manufacturer-form {
    .status-switch-container {
      background-color: var(--el-bg-color-page);
      border-color: var(--el-border-color);
    }
  }
}
</style>