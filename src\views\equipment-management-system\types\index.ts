// 制造商数据模型
export interface Manufacturer {
  equipment_manufacturer_id: number;
  manufacturer_name: string;
  remarks?: string;
  enabled: boolean;
  create_time: string;
  creator: string;
  contacts?: Contact[];
}

// 供应商数据模型
export interface Supplier {
  equipment_supplier_id: number;
  supplier_name: string;
  supplier_address?: string;
  remarks?: string;
  enabled: boolean;
  equipment_manufacturer_id: number;
  equipment_manufacturer_name: string;
  create_time: string;
  creator: string;
  contacts?: Contact[];
}

// 联系人数据模型
export interface Contact {
  id: number;
  contact_name: string;
  contact_phone?: string;
  contact_email?: string;
  contact_role?: string;
  create_time: string;
  creator: string;
  entity_type: "manufacturer" | "supplier";
  entity_id: number;
  entity_name: string;
}

// 表单数据类型
export interface ManufacturerFormData {
  manufacturer_name: string;
  remarks?: string;
  enabled: boolean;
}

export interface SupplierFormData {
  supplier_name: string;
  supplier_address?: string;
  remarks?: string;
  enabled: boolean;
  equipment_manufacturer_id: number;
}

export interface ContactFormData {
  contact_name: string;
  contact_phone?: string;
  contact_email?: string;
  contact_role?: string;
  entity_type: "manufacturer" | "supplier";
  entity_id: number;
}
