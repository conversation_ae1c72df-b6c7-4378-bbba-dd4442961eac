# ContactForm 组件迁移指南

## 📋 组件概述

`contact-form.vue` 是从React设备管理系统迁移而来的联系人表单组件，用于联系人信息的录入和编辑功能，支持制造商/供应商关联类型的动态切换。

## 🎯 功能特性对比

### 原React组件特性
- ✅ 联系人姓名输入（必填，1-50字符验证）
- ✅ 联系电话输入（可选，手机号码格式验证）
- ✅ 邮箱地址输入（可选，邮箱格式验证）
- ✅ 职位输入（可选，最多50字符）
- ✅ 关联类型选择（必填，制造商/供应商）
- ✅ 关联实体选择（必填，根据类型动态显示）
- ✅ 实体过滤（只显示已启用的制造商/供应商）
- ✅ 表单验证（Zod schema + 正则表达式）
- ✅ 加载状态支持
- ✅ 新增/编辑模式支持
- ✅ 基于React Hook Form + shadcn/ui

### Vue3迁移后特性
- ✅ 完全对等的表单字段和验证
- ✅ Element Plus表单组件
- ✅ Vue3 reactive响应式数据管理
- ✅ Element Plus验证规则 + 自定义验证器
- ✅ computed自动过滤制造商/供应商
- ✅ watch监听类型变化自动重置选择
- ✅ 保持一致的用户交互
- ✅ 基于Vue3 Composition API + Element Plus
- 🎉 **增强功能**：实体空状态提示
- 🎉 **增强功能**：下拉搜索过滤功能
- 🎉 **增强功能**：响应式布局适配
- 🎉 **增强功能**：明暗主题支持
- 🎉 **增强功能**：表单方法暴露
- 🎉 **增强功能**：自动验证清除

## 📦 API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| initialData | `Contact` | `undefined` | 初始数据（编辑模式时传入） |
| manufacturers | `Manufacturer[]` | `[]` | 制造商列表（必填） |
| suppliers | `Supplier[]` | `[]` | 供应商列表（必填） |
| onSubmit | `(data: ContactFormData) => void` | - | 表单提交回调（必填） |
| onCancel | `() => void` | - | 取消操作回调（必填） |
| isLoading | `boolean` | `false` | 是否处于加载状态 |

### Contact 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| equipmentContactId | `number?` | 联系人ID |
| contactName | `string?` | 联系人姓名 |
| contactPhone | `string?` | 联系电话 |
| contactEmail | `string?` | 邮箱地址 |
| contactRole | `string?` | 职位 |
| entityType | `"manufacturer" \| "supplier"?` | 关联类型 |
| entityId | `number?` | 关联实体ID |
| entityName | `string?` | 关联实体名称 |
| createTime | `string?` | 创建时间 |
| creator | `string?` | 创建者 |
| editTime | `string?` | 编辑时间 |
| editor | `string?` | 编辑者 |

### Manufacturer 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| equipmentManufacturerId | `number?` | 制造商ID |
| manufacturerName | `string?` | 制造商名称 |
| remarks | `string?` | 备注信息 |
| enabled | `boolean?` | 启用状态 |
| createTime | `string?` | 创建时间 |
| creator | `string?` | 创建者 |
| editTime | `string?` | 编辑时间 |
| editor | `string?` | 编辑者 |

### Supplier 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| equipmentSupplierId | `number?` | 供应商ID |
| supplierName | `string?` | 供应商名称 |
| supplierAddress | `string?` | 供应商地址 |
| remarks | `string?` | 备注信息 |
| enabled | `boolean?` | 启用状态 |
| equipmentManufacturerId | `number?` | 关联制造商ID |
| manufacturerName | `string?` | 制造商名称 |
| createTime | `string?` | 创建时间 |
| creator | `string?` | 创建者 |
| editTime | `string?` | 编辑时间 |
| editor | `string?` | 编辑者 |

### ContactFormData 接口

| 属性 | 类型 | 说明 |
|------|------|------|
| contactName | `string` | 联系人姓名（必填） |
| contactPhone | `string?` | 联系电话（可选） |
| contactEmail | `string?` | 邮箱地址（可选） |
| contactRole | `string?` | 职位（可选） |
| entityType | `"manufacturer" \| "supplier"` | 关联类型（必填） |
| entityId | `number` | 关联实体ID（必填） |

### 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getRef | - | `FormInstance` | 获取表单实例引用 |
| resetForm | - | `void` | 重置表单到初始状态 |
| handleSubmit | - | `void` | 手动触发表单提交 |

## 📦 使用方法

### 基本使用 - 新增模式

```vue
<script setup lang="ts">
import ContactForm from "@/views/equipment/components/contact-form.vue";

// 制造商列表（通常从API获取）
const manufacturerList = ref<Manufacturer[]>([
  { 
    equipmentManufacturerId: 1, 
    manufacturerName: "华为技术有限公司", 
    enabled: true 
  },
  { 
    equipmentManufacturerId: 2, 
    manufacturerName: "小米科技有限公司", 
    enabled: true 
  }
]);

// 供应商列表（通常从API获取）
const supplierList = ref<Supplier[]>([
  { 
    equipmentSupplierId: 1, 
    supplierName: "深圳创新供应链", 
    enabled: true 
  },
  { 
    equipmentSupplierId: 2, 
    supplierName: "上海智能物流", 
    enabled: true 
  }
]);

const handleSubmit = (data: ContactFormData) => {
  console.log("提交数据:", data);
  // 调用API创建联系人
  createContact(data);
};

const handleCancel = () => {
  console.log("取消操作");
  // 关闭对话框或返回列表页
};
</script>

<template>
  <ContactForm
    :manufacturers="manufacturerList"
    :suppliers="supplierList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>
```

### 编辑模式使用

```vue
<script setup lang="ts">
import { ref } from "vue";
import ContactForm from "@/views/equipment/components/contact-form.vue";

// 编辑数据
const contactData = ref({
  equipmentContactId: 1,
  contactName: "张伟",
  contactPhone: "13800138001",
  contactEmail: "<EMAIL>",
  contactRole: "技术经理",
  entityType: "manufacturer",
  entityId: 1,
  entityName: "华为技术有限公司",
  createTime: "2023-01-20 11:00:00"
});

// 制造商和供应商列表
const manufacturerList = ref<Manufacturer[]>([...]);
const supplierList = ref<Supplier[]>([...]);
const isLoading = ref(false);

const handleSubmit = async (data: ContactFormData) => {
  try {
    isLoading.value = true;
    await updateContact(contactData.value.equipmentContactId, data);
    ElMessage.success("更新成功");
  } catch (error) {
    ElMessage.error("更新失败");
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <ContactForm
    :initial-data="contactData"
    :manufacturers="manufacturerList"
    :suppliers="supplierList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

### 实体数据管理

```vue
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";

const manufacturerList = ref<Manufacturer[]>([]);
const supplierList = ref<Supplier[]>([]);

// 加载制造商列表
const loadManufacturers = async () => {
  try {
    const data = await equipmentManufacturerQueryController.getManufacturers();
    // 组件会自动过滤启用状态，这里无需过滤
    manufacturerList.value = data;
  } catch (error) {
    ElMessage.error("加载制造商列表失败");
    manufacturerList.value = [];
  }
};

// 加载供应商列表
const loadSuppliers = async () => {
  try {
    const data = await equipmentSupplierQueryController.getSuppliers();
    // 组件会自动过滤启用状态，这里无需过滤
    supplierList.value = data;
  } catch (error) {
    ElMessage.error("加载供应商列表失败");
    supplierList.value = [];
  }
};

// 监听实体列表变化
watch([manufacturerList, supplierList], ([newManufacturers, newSuppliers]) => {
  const enabledManufacturers = newManufacturers.filter(m => m.enabled).length;
  const enabledSuppliers = newSuppliers.filter(s => s.enabled).length;
  
  if (enabledManufacturers === 0 && enabledSuppliers === 0) {
    ElMessage.warning("暂无可用的制造商和供应商，请先添加相关数据");
  }
}, { immediate: true });

onMounted(() => {
  loadManufacturers();
  loadSuppliers();
});
</script>

<template>
  <ContactForm
    :manufacturers="manufacturerList"
    :suppliers="supplierList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>
```

### 在对话框中使用

```vue
<script setup lang="ts">
import { h } from "vue";
import { addDialog } from "@/components/ReDialog";
import ContactForm from "@/views/equipment/components/contact-form.vue";

function openContactDialog(action: "新增" | "编辑", data?: Contact) {
  addDialog({
    title: `${action}联系人`,
    width: "700px",
    draggable: true,
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: ({ options }) => h(ContactForm, {
      initialData: data,
      manufacturers: manufacturerList.value,
      suppliers: supplierList.value,
      onSubmit: async (formData) => {
        try {
          if (action === "新增") {
            await createContact(formData);
          } else {
            await updateContact(data?.equipmentContactId, formData);
          }
          ElMessage.success(`${action}成功`);
          done(); // 关闭对话框
          onSearch(); // 刷新列表
        } catch (error) {
          ElMessage.error(`${action}失败`);
        }
      },
      onCancel: () => done()
    })
  });
}

// 使用示例
const handleAdd = () => openContactDialog("新增");
const handleEdit = (row: Contact) => openContactDialog("编辑", row);
</script>
```

## 🔄 迁移现有代码

### 1. 替换React表单组件

**原React代码:**
```tsx
import { ContactForm } from "@/components/contact-form";

const formSchema = z.object({
  contact_name: z.string().min(1).max(50),
  contact_phone: z.string().regex(/^1[3-9]\d{9}$/).optional().or(z.literal("")),
  contact_email: z.string().email().optional().or(z.literal("")),
  contact_role: z.string().max(50).optional(),
  entity_type: z.enum(["manufacturer", "supplier"]),
  entity_id: z.number().min(1),
});

<ContactForm 
  initialData={contactData}
  manufacturers={manufacturerList}
  suppliers={supplierList}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  isLoading={isLoading}
/>
```

**Vue3代码:**
```vue
<script setup lang="ts">
import ContactForm from "@/views/equipment/components/contact-form.vue";

// 验证规则已内置在组件中，无需外部定义
const handleSubmit = (data: ContactFormData) => {
  // 处理提交逻辑
};
</script>

<template>
  <ContactForm 
    :initial-data="contactData"
    :manufacturers="manufacturerList"
    :suppliers="supplierList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
    :is-loading="isLoading"
  />
</template>
```

### 2. 验证规则迁移

**React (Zod schema):**
```typescript
const formSchema = z.object({
  contact_name: z.string().min(1, "联系人姓名不能为空").max(50, "联系人姓名不能超过50个字符"),
  contact_phone: z.string().regex(/^1[3-9]\d{9}$/, "请输入正确的手机号码").optional().or(z.literal("")),
  contact_email: z.string().email("请输入正确的邮箱地址").optional().or(z.literal("")),
  contact_role: z.string().max(50, "职位不能超过50个字符").optional(),
  entity_type: z.enum(["manufacturer", "supplier"]),
  entity_id: z.number().min(1, "请选择关联实体"),
});
```

**Vue3 (Element Plus rules + 自定义验证器):**
```typescript
// 已内置在组件中
const phoneValidator = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    callback();
  }
};

const emailValidator = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    callback(new Error("请输入正确的邮箱地址"));
  } else {
    callback();
  }
};

const formRules = {
  contactName: [
    { required: true, message: "联系人姓名不能为空", trigger: "blur" },
    { max: 50, message: "联系人姓名不能超过50个字符", trigger: "blur" }
  ],
  contactPhone: [
    { validator: phoneValidator, trigger: "blur" }
  ],
  contactEmail: [
    { validator: emailValidator, trigger: "blur" }
  ],
  entityType: [
    { required: true, message: "请选择关联类型", trigger: "change" }
  ],
  entityId: [
    { type: "number", min: 1, message: "请选择关联实体", trigger: "change" }
  ]
};
```

### 3. 实体动态切换逻辑迁移

**React watch + getEntityOptions:**
```typescript
const entityType = form.watch("entity_type");

const getEntityOptions = () => {
  if (entityType === "manufacturer") {
    return manufacturers
      .filter(m => m.enabled)
      .map(m => ({
        value: m.equipment_manufacturer_id,
        label: m.manufacturer_name
      }));
  } else {
    return suppliers
      .filter(s => s.enabled)
      .map(s => ({
        value: s.equipment_supplier_id,
        label: s.supplier_name
      }));
  }
};
```

**Vue3 computed + watch:**
```typescript
// 组件内部使用computed自动过滤
const entityOptions = computed(() => {
  if (formData.entityType === "manufacturer") {
    return props.manufacturers
      .filter(m => m.enabled)
      .map(m => ({
        value: m.equipmentManufacturerId!,
        label: m.manufacturerName!
      }));
  } else {
    return props.suppliers
      .filter(s => s.enabled)
      .map(s => ({
        value: s.equipmentSupplierId!,
        label: s.supplierName!
      }));
  }
});

// 监听实体类型变化，重置实体ID
watch(
  () => formData.entityType,
  (newType, oldType) => {
    if (newType !== oldType) {
      formData.entityId = 0;
      // 清除验证错误
      formRef.value?.clearValidate('entityId');
    }
  }
);
```

## 🎨 样式定制

### 自定义表单样式

```vue
<template>
  <ContactForm 
    class="custom-contact-form"
    :manufacturers="manufacturerList"
    :suppliers="supplierList"
    :on-submit="handleSubmit"
    :on-cancel="handleCancel"
  />
</template>

<style scoped>
.custom-contact-form {
  :deep(.el-form-item__label) {
    color: var(--el-color-primary);
    font-weight: 600;
  }
  
  :deep(.entity-empty-tip) {
    background-color: var(--el-color-warning-light-9);
    padding: 8px;
    border-radius: 4px;
  }
}
</style>
```

### 自定义实体选择器

```vue
<style scoped>
.custom-contact-form {
  :deep(.el-select) {
    .el-input__wrapper {
      border-color: var(--el-color-primary);
    }
    
    .el-select__placeholder {
      color: var(--el-text-color-placeholder);
    }
  }
}
</style>
```

## 🔧 技术实现细节

### 实体动态过滤

Vue3使用computed自动过滤已启用的实体：

```typescript
const entityOptions = computed(() => {
  if (formData.entityType === "manufacturer") {
    return props.manufacturers
      .filter(m => m.enabled)
      .map(m => ({
        value: m.equipmentManufacturerId!,
        label: m.manufacturerName!
      }));
  } else {
    return props.suppliers
      .filter(s => s.enabled)
      .map(s => ({
        value: s.equipmentSupplierId!,
        label: s.supplierName!
      }));
  }
});
```

### 表单数据管理

使用reactive管理表单状态，支持双向绑定：

```typescript
const formData = reactive<ContactFormData>({
  contactName: props.initialData?.contactName || "",
  contactPhone: props.initialData?.contactPhone || "",
  contactEmail: props.initialData?.contactEmail || "",
  contactRole: props.initialData?.contactRole || "",
  entityType: props.initialData?.entityType || "manufacturer",
  entityId: props.initialData?.entityId || 0
});
```

### 自定义验证器

Element Plus支持自定义验证器，处理复杂验证逻辑：

```typescript
const phoneValidator = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(); // 空值允许通过
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    callback();
  }
};
```

### 类型切换处理

watch监听类型变化，自动重置实体选择：

```typescript
watch(
  () => formData.entityType,
  (newType, oldType) => {
    if (newType !== oldType) {
      formData.entityId = 0;
      // 清除验证错误
      formRef.value?.clearValidate('entityId');
    }
  }
);
```

## 🧪 测试用例

```vue
<script setup lang="ts">
// 测试数据
const testCases = [
  {
    name: "新增模式",
    data: undefined,
    manufacturers: mockManufacturers,
    suppliers: mockSuppliers,
    expected: "空表单，默认选择制造商类型"
  },
  {
    name: "编辑模式 - 制造商联系人",
    data: {
      contactName: "张伟",
      contactPhone: "13800138001",
      contactEmail: "<EMAIL>",
      contactRole: "技术经理",
      entityType: "manufacturer",
      entityId: 1
    },
    manufacturers: mockManufacturers,
    suppliers: mockSuppliers,
    expected: "表单填充制造商联系人数据"
  },
  {
    name: "编辑模式 - 供应商联系人",
    data: {
      contactName: "李娜",
      contactPhone: "13900139002",
      contactEmail: "<EMAIL>",
      contactRole: "销售总监",
      entityType: "supplier",
      entityId: 1
    },
    manufacturers: mockManufacturers,
    suppliers: mockSuppliers,
    expected: "表单填充供应商联系人数据"
  },
  {
    name: "制造商为空",
    data: undefined,
    manufacturers: [],
    suppliers: mockSuppliers,
    expected: "选择制造商类型时显示空状态提示"
  },
  {
    name: "供应商为空",
    data: undefined,
    manufacturers: mockManufacturers,
    suppliers: [],
    expected: "选择供应商类型时显示空状态提示"
  },
  {
    name: "实体类型切换",
    data: { entityType: "manufacturer", entityId: 1 },
    manufacturers: mockManufacturers,
    suppliers: mockSuppliers,
    expected: "切换到供应商时自动重置实体选择"
  }
];

// 验证测试
const runValidationTests = () => {
  // 测试必填验证
  // 测试手机号码格式验证
  // 测试邮箱格式验证
  // 测试实体选择验证
  // 测试类型切换逻辑
};
</script>
```

## 🚀 最佳实践

1. **数据准备**: 确保传入有效的制造商和供应商列表，至少包含一个启用的实体
2. **数据初始化**: 编辑模式时确保传入完整且正确的initialData
3. **验证处理**: 手机号码和邮箱支持空值，但格式必须正确
4. **类型切换**: 实体类型变更时自动重置实体选择，避免无效数据
5. **错误处理**: 在onSubmit回调中添加完善的错误处理逻辑
6. **加载状态**: 在异步操作期间正确设置isLoading状态
7. **数据验证**: 利用组件内置验证规则，也可以通过ref进行自定义验证
8. **实体管理**: 定期检查制造商和供应商列表的有效性，提供友好的空状态提示
9. **用户体验**: 适当使用ElMessage提供操作反馈
10. **响应式**: 组件已内置响应式支持，无需额外处理

## 📋 迁移检查清单

- [ ] 导入ContactForm组件
- [ ] 定义ContactFormData接口
- [ ] 准备制造商数据列表
- [ ] 准备供应商数据列表
- [ ] 实现onSubmit回调函数
- [ ] 实现onCancel回调函数
- [ ] 测试新增模式功能
- [ ] 测试编辑模式功能
- [ ] 验证实体类型切换功能
- [ ] 测试实体过滤逻辑
- [ ] 验证手机号码格式验证
- [ ] 验证邮箱格式验证
- [ ] 验证表单验证规则
- [ ] 测试加载状态
- [ ] 检查实体空状态处理
- [ ] 验证响应式布局
- [ ] 测试明暗主题切换
- [ ] 更新相关文档

## 🔗 相关文件

- `contact-form.vue` - 主组件文件
- `contact-form-example.vue` - 使用示例
- `contact-form-migration-guide.md` - 本迁移指南

## ❓ 常见问题

**Q: 实体列表为空时怎么办？**
A: 组件会显示友好的空状态提示，建议在外部检查并引导用户先添加相关数据。

**Q: 如何处理实体数据加载失败？**
A: 建议在调用组件前先处理异常，提供空数组作为fallback。

**Q: 实体类型切换时数据会丢失吗？**
A: 组件会自动重置实体选择，避免无效数据，同时清除相关验证错误。

**Q: 能否自定义验证规则？**
A: 可以修改组件内的验证器函数，或通过ref获取表单实例进行自定义验证。

**Q: 支持实体数据的实时更新吗？**
A: 支持，manufacturers和suppliers是响应式的，更新后组件会自动重新渲染选项。

**Q: 如何处理联系方式格式验证？**
A: 组件已内置手机号码和邮箱格式验证器，支持空值但格式必须正确。

**Q: 能否扩展更多联系方式字段？**
A: 可以扩展组件，添加微信、QQ等字段和相应的验证规则。